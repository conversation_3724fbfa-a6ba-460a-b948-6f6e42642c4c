---
type: "always_apply"
---

## 目录规范：
- 后端目录:server
- 前端目录:ui
- 文档目录:docs
- 脚本目录:scripts
- 配置目录:deployment
- 备份目录:backup
- 日志目录:logs

## 创建文档规范
- 减少创建修复文档，只创建必要的文档
- 为了逐步统一命名规范，后续把箱子统一命名case，饰品统一命名skin

## 系统环境
- 当前开发环境是服务器环境，ip：************
- 数据库是独立的阿里云RDS
- 开发环境：django端口9000，ui端口3000，socket端口映射到了域名：https://socket.cs2.net.cn
- 生成环境docker容器：服务端已经映射了域名：https://api.cs2.net.cn，socket映射了域名：https://socket.cs2.net.cn
- 所有后端服务都是docker容器，如果要验证服务是否正常，直接查看容器日志，修改完代码后，要重启容器才能生效
- python版本：3.8
- django版本：3.2
- server目录下使用虚拟环境：source venv/bin/activate
- 只要更新到docker，就有自动任务在执行开箱和对战，不要使用模拟数据，demo页面除外

## 代码提交规范
- 代码提交前，先运行前端和后端的测试用例，确保没有问题
- 前端测试页面存放在ui/page/demo，并加入演示导航ui/page/demo/index.vue
- 前端代码完成后运行npm run build，确保没有问题
- 后端代码完成后运行python manage.py test，确保没有问题

## bug修复规范
- 前后端结合分析问题，找到最佳修复方案
- server端修改后要重启容器让代码生效
- 修复后要进行回归测试，确保没有引入新问题

## UI要求
- 高端、大气、酷炫、互动性强、参与性强的CSGO开箱网站
- 实时、同步的动画
- 各个板块与整站风格统一
- tailwind原子化css

## 主要功能模块描述
- 开箱：开箱结果由后端返回，调整后端开箱结果代码要慎重。前端动画停止线必须指示在开箱结果。视觉上的真实性必须保证。
- 开箱对战：实时、同步的动画是关键，必须保证视觉上的真实性，保证每次停止线在开箱结果上。