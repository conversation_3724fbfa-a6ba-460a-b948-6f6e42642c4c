# CSGO Skins 项目 - Augment 智能体规则

## 项目概述

这是一个CSGO皮肤开箱游戏平台，包含前端（Nuxt.js + Vue 3）和后端（Django）的完整系统。Augment智能体应该专注于代码质量提升、性能优化和架构改进。

## 代码分析重点

### 前端代码分析
- **Vue 3 Composition API** 使用是否规范
- **TypeScript** 类型定义是否完整和准确
- **响应式设计** 是否合理
- **组件复用性** 和 **可维护性**
- **性能优化** 机会（懒加载、缓存、虚拟滚动等）

### 后端代码分析
- **Django ORM** 查询优化
- **数据库设计** 合理性
- **API设计** 的RESTful规范
- **安全性** 检查（SQL注入、XSS防护等）
- **异步处理** 和 **并发控制**

### 游戏逻辑分析
- **开箱算法** 的公平性和随机性
- **实时通信** 的稳定性和效率
- **动画性能** 优化
- **用户体验** 流畅性

## 重构建议策略

### 代码结构优化
1. **组件拆分**：将大型组件拆分为更小的、可复用的组件
2. **逻辑分离**：将业务逻辑从UI组件中提取到composables
3. **状态管理**：优化Pinia store的结构和状态更新逻辑
4. **API封装**：统一API调用方式，增加错误处理和重试机制

### 性能优化建议
1. **前端优化**：
   - 图片懒加载和压缩
   - 组件懒加载
   - 虚拟滚动（长列表）
   - 动画性能优化（使用CSS3、requestAnimationFrame）
   - 内存泄漏检查

2. **后端优化**：
   - 数据库查询优化（索引、N+1查询）
   - 缓存策略（Redis缓存热点数据）
   - 异步任务处理（Celery队列优化）
   - API响应压缩

### 安全性增强
1. **输入验证**：所有用户输入必须严格验证
2. **权限控制**：细粒度的权限检查
3. **数据加密**：敏感数据加密存储
4. **API安全**：CSRF保护、速率限制

## 具体改进建议

### 前端改进
```typescript
// 建议：将复杂的开箱动画逻辑提取到独立的composable
// 当前可能存在的问题：动画逻辑与UI组件耦合过紧
export const useCaseOpening = () => {
  // 动画状态管理
  // 音效控制
  // 结果处理
  // 错误处理
}

// 建议：优化WebSocket连接管理
// 当前可能存在的问题：连接不稳定，重连逻辑不完善
export const useWebSocket = () => {
  // 自动重连
  // 心跳检测
  // 消息队列
  // 连接状态管理
}
```

### 后端改进
```python
# 建议：优化开箱算法，确保公平性
# 当前可能存在的问题：随机性不够，容易被预测
def generate_fair_drop(case_drops, user_id):
    # 使用加密安全的随机数生成器
    # 添加用户特定的种子
    # 记录随机数生成过程用于审计

# 建议：优化数据库查询
# 当前可能存在的问题：N+1查询问题
def get_user_case_history(user_id):
    # 使用select_related/prefetch_related
    # 批量查询优化
    # 缓存热点数据
```

### 架构改进
1. **微服务拆分**：考虑将支付、交易、游戏等模块拆分为独立服务
2. **消息队列**：使用Redis Streams或RabbitMQ处理异步任务
3. **监控系统**：集成APM工具监控性能
4. **日志系统**：结构化日志，便于问题排查

## 代码质量检查清单

### 前端检查项
- [ ] TypeScript类型覆盖率 > 90%
- [ ] 组件单元测试覆盖率 > 80%
- [ ] 代码重复率 < 5%
- [ ] 组件复杂度（圈复杂度 < 10）
- [ ] 包大小优化（Tree shaking、代码分割）

### 后端检查项
- [ ] 数据库查询性能（查询时间 < 100ms）
- [ ] API响应时间（平均 < 200ms）
- [ ] 错误处理覆盖率 100%
- [ ] 日志记录完整性
- [ ] 安全漏洞扫描通过

### 游戏逻辑检查项
- [ ] 开箱算法公平性验证
- [ ] 实时通信延迟 < 100ms
- [ ] 动画帧率 > 60fps
- [ ] 并发用户支持 > 1000
- [ ] 数据一致性保证

## 重构优先级

### 高优先级（立即处理）
1. **安全漏洞**：任何安全相关的问题
2. **性能瓶颈**：影响用户体验的性能问题
3. **数据一致性**：可能导致数据错误的问题
4. **关键功能bug**：影响核心游戏功能的问题

### 中优先级（计划处理）
1. **代码重构**：提高代码质量和可维护性
2. **性能优化**：非关键路径的性能改进
3. **用户体验**：界面和交互优化
4. **监控完善**：增加监控和日志

### 低优先级（长期规划）
1. **架构升级**：大规模架构调整
2. **技术栈升级**：框架和库的版本升级
3. **功能扩展**：新功能开发
4. **文档完善**：代码文档和API文档

## 代码审查标准

### 必须通过
- 代码编译无错误
- 类型检查通过
- 单元测试通过
- 安全扫描通过
- 性能基准测试通过

### 建议改进
- 代码可读性
- 命名规范性
- 注释完整性
- 错误处理
- 边界情况处理

## 自动化建议

### CI/CD改进
1. **自动化测试**：单元测试、集成测试、E2E测试
2. **代码质量检查**：ESLint、Prettier、SonarQube
3. **性能监控**：Lighthouse、WebPageTest
4. **安全扫描**：OWASP ZAP、Snyk

### 监控告警
1. **错误率监控**：API错误率、前端错误率
2. **性能监控**：响应时间、吞吐量
3. **业务监控**：开箱成功率、支付成功率
4. **系统监控**：CPU、内存、磁盘使用率

## 最佳实践建议

### 前端最佳实践
- 使用Vue 3 Composition API
- 合理使用TypeScript
- 组件设计遵循单一职责原则
- 状态管理使用Pinia
- 样式使用Tailwind CSS

### 后端最佳实践
- 遵循Django最佳实践
- 使用Django REST framework
- 数据库查询优化
- 异步任务使用Celery
- 缓存策略使用Redis

### 游戏开发最佳实践
- 随机数生成使用加密安全的算法
- 实时通信使用WebSocket
- 动画使用requestAnimationFrame
- 音效使用Web Audio API
- 数据同步使用乐观更新

## 技术债务管理

### 识别技术债务
- 代码重复
- 过时的依赖
- 性能瓶颈
- 安全漏洞
- 测试覆盖率不足

### 技术债务优先级
1. **安全相关**：立即修复
2. **性能相关**：高优先级
3. **维护性相关**：中优先级
4. **文档相关**：低优先级

### 技术债务偿还计划
- 每周分配20%时间处理技术债务
- 新功能开发时同时处理相关技术债务
- 定期进行代码重构
- 持续监控技术债务指标
