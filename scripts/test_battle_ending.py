#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试对战结束修复效果
"""

import os
import sys
import time
from datetime import datetime, timedelta

# 设置Django环境
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'server'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

from django.utils import timezone
from box.models import CaseRoom, CaseRoomRound, GameState, RoomType
from box.business_room import run_battle_room

def analyze_stuck_battles():
    """分析卡在进行中的对战"""
    
    print("🔍 分析卡在进行中的对战房间")
    print("=" * 50)
    
    # 查找长时间运行的房间
    time_thresholds = [
        (5, "5分钟"),
        (15, "15分钟"), 
        (30, "30分钟"),
        (60, "1小时"),
        (120, "2小时")
    ]
    
    for minutes, label in time_thresholds:
        threshold_time = timezone.now() - timedelta(minutes=minutes)
        stuck_rooms = CaseRoom.objects.filter(
            state=GameState.Running.value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=threshold_time
        )
        
        if stuck_rooms.exists():
            print(f"\n⚠️ 运行超过{label}的房间 ({stuck_rooms.count()} 个):")
            
            for room in stuck_rooms[:5]:  # 只显示前5个
                # 检查轮次状态
                total_rounds = room.rounds.count()
                opened_rounds = room.rounds.filter(opened=True).count()
                unopened_rounds = room.rounds.filter(opened=False).count()
                
                # 检查参与者
                bets_count = room.bets.count()
                
                # 计算运行时间
                running_time = timezone.now() - room.update_time
                running_minutes = int(running_time.total_seconds() / 60)
                
                print(f"  房间 {room.short_id}:")
                print(f"    - 运行时间: {running_minutes} 分钟")
                print(f"    - 参与者: {bets_count}/{room.max_joiner}")
                print(f"    - 轮次状态: {opened_rounds}/{total_rounds} 已完成, {unopened_rounds} 未开启")
                
                # 分析问题
                if unopened_rounds == 0 and opened_rounds == total_rounds:
                    print(f"    - 🚨 问题: 所有轮次已完成但房间未结束")
                elif unopened_rounds > 0:
                    print(f"    - 🚨 问题: 还有 {unopened_rounds} 轮未开启")
                else:
                    print(f"    - ❓ 问题: 轮次状态异常")

def test_battle_ending_logic():
    """测试对战结束逻辑"""
    
    print("\n🧪 测试对战结束逻辑")
    print("=" * 50)
    
    # 查找可测试的运行中房间
    test_rooms = CaseRoom.objects.filter(
        state=GameState.Running.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
    ).order_by('-update_time')[:3]
    
    if not test_rooms.exists():
        print("❌ 没有找到运行中的房间进行测试")
        return
    
    print(f"🔍 找到 {test_rooms.count()} 个运行中的房间:")
    
    for room in test_rooms:
        print(f"\n  测试房间 {room.short_id}:")
        
        # 检查轮次状态
        total_rounds = room.rounds.count()
        opened_rounds = room.rounds.filter(opened=True).count()
        unopened_rounds = room.rounds.filter(opened=False).count()
        
        print(f"    - 轮次状态: {opened_rounds}/{total_rounds} 已完成")
        print(f"    - 未开启轮次: {unopened_rounds}")
        
        # 如果所有轮次都完成了，这个房间应该结束
        if unopened_rounds == 0 and opened_rounds == total_rounds:
            print(f"    - 🎯 这个房间应该结束，尝试手动触发...")
            
            try:
                # 调用run_battle_room来处理结束逻辑
                run_battle_room(room.uid)
                
                # 重新检查房间状态
                room.refresh_from_db()
                if room.state == GameState.End.value:
                    print(f"    - ✅ 房间已成功结束")
                else:
                    print(f"    - ⚠️ 房间状态仍为: {room.state}")
                    
            except Exception as e:
                print(f"    - ❌ 测试失败: {e}")
        
        elif unopened_rounds > 0:
            print(f"    - 🔄 房间还有轮次未完成，尝试推进...")
            
            try:
                # 调用run_battle_room来推进下一轮
                run_battle_room(room.uid)
                
                # 重新检查轮次状态
                new_unopened = room.rounds.filter(opened=False).count()
                if new_unopened < unopened_rounds:
                    print(f"    - ✅ 成功推进轮次，剩余未开启: {new_unopened}")
                else:
                    print(f"    - ⚠️ 轮次未推进，仍有 {new_unopened} 轮未开启")
                    
            except Exception as e:
                print(f"    - ❌ 测试失败: {e}")

def monitor_battle_progression():
    """监控对战推进"""
    
    print("\n🔍 监控对战推进 (按Ctrl+C停止):")
    
    # 记录初始状态
    initial_states = {}
    rooms = CaseRoom.objects.filter(
        state=GameState.Running.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
    )
    
    for room in rooms:
        initial_states[room.uid] = {
            'opened_rounds': room.rounds.filter(opened=True).count(),
            'total_rounds': room.rounds.count(),
            'state': room.state
        }
    
    print(f"  初始监控 {len(initial_states)} 个运行中的房间")
    
    try:
        for i in range(60):  # 监控60次，每次10秒
            time.sleep(10)
            
            changes_detected = False
            current_rooms = CaseRoom.objects.filter(uid__in=initial_states.keys())
            
            for room in current_rooms:
                initial = initial_states[room.uid]
                current_opened = room.rounds.filter(opened=True).count()
                
                if (room.state != initial['state'] or 
                    current_opened != initial['opened_rounds']):
                    
                    print(f"  🔄 房间 {room.short_id} 变化:")
                    print(f"    状态: {initial['state']} -> {room.state}")
                    print(f"    已完成轮次: {initial['opened_rounds']} -> {current_opened}")
                    
                    # 更新记录
                    initial_states[room.uid] = {
                        'opened_rounds': current_opened,
                        'total_rounds': room.rounds.count(),
                        'state': room.state
                    }
                    changes_detected = True
            
            if not changes_detected and i % 6 == 0:  # 每60秒输出一次状态
                print(f"  ⏰ {datetime.now().strftime('%H:%M:%S')} - 监控中...")
    
    except KeyboardInterrupt:
        print(f"\n  监控已停止")

def check_async_system():
    """检查异步系统状态"""
    
    print("\n🔧 检查异步系统状态:")
    
    try:
        from box.enhanced_async_system import get_battle_async_processor
        processor = get_battle_async_processor()
        stats = processor.get_stats()
        
        print(f"  异步处理器状态: {'运行中' if stats['is_running'] else '未运行'}")
        print(f"  活跃任务: {stats['active_tasks']}")
        print(f"  队列大小: {stats['queue_size']}")
        print(f"  已完成任务: {stats['completed_tasks']}")
        print(f"  失败任务: {stats['failed_tasks']}")
        
        if not stats['is_running']:
            print("  ⚠️ 异步处理器未运行，对战推进可能受影响")
        
    except Exception as e:
        print(f"  ❌ 检查异步系统失败: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="测试对战结束修复效果")
    parser.add_argument("--analyze", action="store_true", help="分析卡住的对战")
    parser.add_argument("--test", action="store_true", help="测试结束逻辑")
    parser.add_argument("--monitor", action="store_true", help="监控对战推进")
    parser.add_argument("--check-async", action="store_true", help="检查异步系统")
    parser.add_argument("--all", action="store_true", help="执行所有检查")
    
    args = parser.parse_args()
    
    if args.all or args.analyze:
        analyze_stuck_battles()
    
    if args.all or args.check_async:
        check_async_system()
    
    if args.all or args.test:
        test_battle_ending_logic()
    
    if args.monitor:
        monitor_battle_progression()
    
    if not any([args.analyze, args.test, args.monitor, args.check_async, args.all]):
        print("请指定操作: --analyze, --test, --monitor, --check-async, 或 --all")
        parser.print_help()
