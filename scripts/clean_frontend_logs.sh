#!/bin/bash

# 清理前端日志脚本
echo "🧹 开始清理前端日志..."

# 定义要清理的目录
UI_DIR="/Users/<USER>/Documents/csgoskins.com.cn/ui"

# 清理 console.log
echo "清理 console.log..."
find "$UI_DIR" -name "*.vue" -o -name "*.ts" -o -name "*.js" | xargs sed -i '' 's/console\.log(/\/\/ console.log(/g'

# 清理 console.warn (保留我们需要的关键日志)
echo "清理 console.warn (保留关键日志)..."
find "$UI_DIR" -name "*.vue" -o -name "*.ts" -o -name "*.js" | xargs sed -i '' '/🎰玩家\|🔍动画\|🚨动画\|❌动画\|✅动画\|🎬动画/!s/console\.warn(/\/\/ console.warn(/g'

# 清理 console.error (保留错误日志)
echo "清理 console.error..."
find "$UI_DIR" -name "*.vue" -o -name "*.ts" -o -name "*.js" | xargs sed -i '' 's/console\.error(/\/\/ console.error(/g'

echo "✅ 前端日志清理完成！"
echo "保留的关键日志标记："
echo "  - 🎰玩家"
echo "  - 🔍动画"
echo "  - 🚨动画"
echo "  - ❌动画"
echo "  - ✅动画"
echo "  - 🎬动画"
