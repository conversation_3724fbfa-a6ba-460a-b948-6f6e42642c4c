#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热修复对战房间问题 - 立即生效
直接修改数据库中的房间状态
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'server'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

from django.utils import timezone
from django.db import transaction
from box.models import CaseRoom, CaseRoomBet, GameState, RoomType

def hotfix_stuck_rooms():
    """热修复卡住的房间"""
    
    print("🚨 开始热修复卡住的对战房间...")
    print("=" * 60)
    
    # 1. 修复长时间卡在满员状态的房间
    threshold_time = timezone.now() - timedelta(minutes=3)  # 3分钟阈值
    stuck_full_rooms = CaseRoom.objects.filter(
        state=GameState.Full.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=threshold_time
    )
    
    print(f"🔍 发现 {stuck_full_rooms.count()} 个长时间卡在满员状态的房间")
    
    fixed_full_rooms = 0
    for room in stuck_full_rooms:
        try:
            with transaction.atomic():
                # 重新获取房间以确保数据最新
                room = CaseRoom.objects.select_for_update().get(id=room.id)
                bets_count = room.bets.count()
                
                print(f"🔧 修复满员房间 {room.short_id}: {bets_count}/{room.max_joiner} 参与者")
                
                if bets_count == room.max_joiner:
                    # 满员房间，直接设置为运行状态
                    room.state = GameState.Running.value
                    room.save()
                    print(f"  ✅ 设置为运行状态")
                    fixed_full_rooms += 1
                elif bets_count > 0:
                    # 有参与者但未满员，重置为可加入状态
                    room.state = GameState.Joinable.value
                    room.save()
                    print(f"  🔄 重置为可加入状态")
                    fixed_full_rooms += 1
                else:
                    # 无参与者，取消房间
                    room.state = GameState.Cancelled.value
                    room.save()
                    print(f"  ❌ 取消房间（无参与者）")
                    fixed_full_rooms += 1
                    
        except Exception as e:
            print(f"  ❌ 修复房间 {room.short_id} 失败: {e}")
    
    # 2. 修复长时间运行的房间
    long_running_threshold = timezone.now() - timedelta(minutes=30)  # 30分钟阈值
    long_running_rooms = CaseRoom.objects.filter(
        state=GameState.Running.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=long_running_threshold
    )
    
    print(f"\n🔍 发现 {long_running_rooms.count()} 个长时间运行的房间")
    
    fixed_running_rooms = 0
    for room in long_running_rooms:
        try:
            with transaction.atomic():
                room = CaseRoom.objects.select_for_update().get(id=room.id)
                bets_count = room.bets.count()
                
                print(f"🔧 检查长时间运行房间 {room.short_id}: {bets_count}/{room.max_joiner} 参与者")
                
                if bets_count > 0:
                    # 有参与者，设置为结束状态
                    room.state = GameState.End.value
                    room.save()
                    print(f"  ✅ 设置为结束状态")
                    fixed_running_rooms += 1
                else:
                    # 无参与者，取消房间
                    room.state = GameState.Cancelled.value
                    room.save()
                    print(f"  ❌ 取消房间（无参与者）")
                    fixed_running_rooms += 1
                    
        except Exception as e:
            print(f"  ❌ 修复房间 {room.short_id} 失败: {e}")
    
    print(f"\n✅ 热修复完成:")
    print(f"  - 修复满员房间: {fixed_full_rooms} 个")
    print(f"  - 修复运行房间: {fixed_running_rooms} 个")
    print(f"  - 总计修复: {fixed_full_rooms + fixed_running_rooms} 个")

def show_current_status():
    """显示当前房间状态"""
    
    print("\n📊 当前房间状态统计:")
    print("-" * 40)
    
    states = [
        (GameState.Initial.value, "初始"),
        (GameState.Joinable.value, "可加入"),
        (GameState.Joining.value, "加入中"),
        (GameState.Full.value, "满员"),
        (GameState.Running.value, "进行中"),
        (GameState.End.value, "已结束"),
        (GameState.Cancelled.value, "已取消")
    ]
    
    total_rooms = 0
    for state_value, state_name in states:
        count = CaseRoom.objects.filter(
            state=state_value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        ).count()
        if count > 0:
            print(f"  {state_name}: {count} 个")
            total_rooms += count
    
    print(f"  总计: {total_rooms} 个对战房间")
    
    # 显示最近的活动
    recent_rooms = CaseRoom.objects.filter(
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__gte=timezone.now() - timedelta(minutes=10)
    ).order_by('-update_time')[:5]
    
    if recent_rooms.exists():
        print(f"\n🕐 最近10分钟活动的房间:")
        for room in recent_rooms:
            state_name = dict(states).get(room.state, f"未知({room.state})")
            bets_count = room.bets.count()
            time_diff = timezone.now() - room.update_time
            print(f"  {room.short_id}: {state_name} ({bets_count}/{room.max_joiner}) - {time_diff.total_seconds():.0f}秒前")

def emergency_fix():
    """紧急修复 - 重置所有问题房间"""
    
    print("\n🚨 执行紧急修复...")
    
    # 找到所有可能有问题的房间
    problem_rooms = CaseRoom.objects.filter(
        state__in=[GameState.Full.value, GameState.Running.value],
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=timezone.now() - timedelta(minutes=2)
    )
    
    print(f"🔍 发现 {problem_rooms.count()} 个可能有问题的房间")
    
    if problem_rooms.count() == 0:
        print("✅ 没有发现问题房间")
        return
    
    confirm = input("⚠️ 确认要重置这些房间吗？(y/N): ")
    if confirm.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    fixed_count = 0
    for room in problem_rooms:
        try:
            with transaction.atomic():
                room = CaseRoom.objects.select_for_update().get(id=room.id)
                bets_count = room.bets.count()
                
                if room.state == GameState.Full.value and bets_count == room.max_joiner:
                    room.state = GameState.Running.value
                    room.save()
                    print(f"✅ 房间 {room.short_id} 设置为运行状态")
                    fixed_count += 1
                elif bets_count > 0:
                    room.state = GameState.End.value
                    room.save()
                    print(f"✅ 房间 {room.short_id} 设置为结束状态")
                    fixed_count += 1
                else:
                    room.state = GameState.Cancelled.value
                    room.save()
                    print(f"✅ 房间 {room.short_id} 已取消")
                    fixed_count += 1
                    
        except Exception as e:
            print(f"❌ 修复房间 {room.short_id} 失败: {e}")
    
    print(f"\n✅ 紧急修复完成，共修复 {fixed_count} 个房间")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="热修复对战房间问题")
    parser.add_argument("--emergency", action="store_true", help="执行紧急修复")
    
    args = parser.parse_args()
    
    # 显示当前状态
    show_current_status()
    
    # 执行热修复
    hotfix_stuck_rooms()
    
    # 如果指定了紧急修复
    if args.emergency:
        emergency_fix()
    
    # 再次显示状态
    show_current_status()
    
    print(f"\n🎯 修复完成时间: {timezone.now()}")
    print("💡 建议: 继续观察房间状态，如有问题请重新运行此脚本")
