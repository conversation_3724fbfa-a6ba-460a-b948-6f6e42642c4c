#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速SQL修复卡住的房间
"""

import os
import sys

# 添加Django路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'server'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

import django
django.setup()

from django.db import connection

def execute_sql(sql, description):
    """执行SQL并显示结果"""
    print(f"\n{description}")
    print("-" * 50)
    
    try:
        with connection.cursor() as cursor:
            cursor.execute(sql)
            
            if sql.strip().upper().startswith('SELECT'):
                # 查询语句，显示结果
                results = cursor.fetchall()
                if results:
                    for row in results:
                        print(row)
                else:
                    print("无结果")
            else:
                # 更新语句，显示影响行数
                affected_rows = cursor.rowcount
                print(f"影响行数: {affected_rows}")
                
    except Exception as e:
        print(f"执行失败: {e}")

def main():
    print("🚨 快速SQL修复卡住的房间")
    print("=" * 60)
    
    # 1. 查看当前状态
    execute_sql("""
        SELECT 
            state,
            COUNT(*) as count,
            CASE 
                WHEN state = 1 THEN '初始'
                WHEN state = 2 THEN '可加入'
                WHEN state = 3 THEN '加入中'
                WHEN state = 4 THEN '满员'
                WHEN state = 5 THEN '进行中'
                WHEN state = 6 THEN '已结束'
                WHEN state = 7 THEN '已取消'
                ELSE '未知'
            END as state_name
        FROM box_caseroom 
        WHERE type IN (1, 3)
        GROUP BY state
        ORDER BY state
    """, "📊 当前房间状态分布")
    
    # 2. 查看卡住的满员房间
    execute_sql("""
        SELECT 
            short_id,
            state,
            max_joiner,
            update_time,
            TIMESTAMPDIFF(MINUTE, update_time, NOW()) as minutes_stuck
        FROM box_caseroom 
        WHERE state = 4
          AND type IN (1, 3)
          AND update_time < DATE_SUB(NOW(), INTERVAL 2 MINUTE)
        ORDER BY update_time
        LIMIT 10
    """, "🔍 卡住的满员房间（前10个）")
    
    # 3. 修复卡住的满员房间
    execute_sql("""
        UPDATE box_caseroom 
        SET 
            state = 5,
            update_time = NOW()
        WHERE state = 4
          AND type IN (1, 3)
          AND update_time < DATE_SUB(NOW(), INTERVAL 2 MINUTE)
    """, "🔧 修复卡住的满员房间")
    
    # 4. 查看长时间运行的房间
    execute_sql("""
        SELECT 
            short_id,
            state,
            max_joiner,
            update_time,
            TIMESTAMPDIFF(MINUTE, update_time, NOW()) as minutes_running
        FROM box_caseroom 
        WHERE state = 5
          AND type IN (1, 3)
          AND update_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
        ORDER BY update_time
        LIMIT 5
    """, "🔍 长时间运行的房间（前5个）")
    
    # 5. 修复长时间运行的房间
    execute_sql("""
        UPDATE box_caseroom 
        SET 
            state = 6,
            update_time = NOW()
        WHERE state = 5
          AND type IN (1, 3)
          AND update_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    """, "🔧 修复长时间运行的房间")
    
    # 6. 显示修复后的状态
    execute_sql("""
        SELECT 
            state,
            COUNT(*) as count,
            CASE 
                WHEN state = 1 THEN '初始'
                WHEN state = 2 THEN '可加入'
                WHEN state = 3 THEN '加入中'
                WHEN state = 4 THEN '满员'
                WHEN state = 5 THEN '进行中'
                WHEN state = 6 THEN '已结束'
                WHEN state = 7 THEN '已取消'
                ELSE '未知'
            END as state_name
        FROM box_caseroom 
        WHERE type IN (1, 3)
        GROUP BY state
        ORDER BY state
    """, "📊 修复后房间状态分布")
    
    print(f"\n✅ 快速修复完成！")
    print("💡 建议:")
    print("  - 如果仍有卡住的房间，请重新运行此脚本")
    print("  - 监控新创建的房间是否正常推进")
    print("  - 检查系统日志确认修复效果")

if __name__ == "__main__":
    main()
