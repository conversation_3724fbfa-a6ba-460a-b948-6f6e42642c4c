#!/bin/bash
# 重启服务并升级异步处理系统

set -e  # 遇到错误立即退出

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
LOG_FILE="/tmp/restart_upgrade.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log "🚀 开始重启服务并升级异步处理系统..."

# 1. 检查当前服务状态
log "📋 检查当前服务状态..."

# 检查Python进程
PYTHON_PIDS=$(pgrep -f "python.*manage.py\|python.*gunicorn\|python.*uwsgi" 2>/dev/null || true)
if [ ! -z "$PYTHON_PIDS" ]; then
    log "🔍 发现Python服务进程: $PYTHON_PIDS"
else
    log "ℹ️ 未发现Python服务进程"
fi

# 检查Celery进程
CELERY_PIDS=$(pgrep -f "celery" 2>/dev/null || true)
if [ ! -z "$CELERY_PIDS" ]; then
    log "🔍 发现Celery进程: $CELERY_PIDS"
else
    log "ℹ️ 未发现Celery进程"
fi

# 检查thworker进程
THWORKER_PIDS=$(pgrep -f "thworker" 2>/dev/null || true)
if [ ! -z "$THWORKER_PIDS" ]; then
    log "🔍 发现thworker进程: $THWORKER_PIDS"
else
    log "ℹ️ 未发现thworker进程"
fi

# 检查Docker容器
if command -v docker &> /dev/null; then
    DOCKER_CONTAINERS=$(docker ps --format "{{.Names}}" 2>/dev/null | grep -E "(django|celery|worker|web)" || true)
    if [ ! -z "$DOCKER_CONTAINERS" ]; then
        log "🔍 发现Docker容器: $DOCKER_CONTAINERS"
    else
        log "ℹ️ 未发现相关Docker容器"
    fi
else
    log "ℹ️ Docker未安装或不可用"
fi

# 2. 备份当前配置
log "💾 备份当前配置..."
BACKUP_DIR="/tmp/backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

if [ -f "$PROJECT_ROOT/server/steambase/settings.py" ]; then
    cp "$PROJECT_ROOT/server/steambase/settings.py" "$BACKUP_DIR/"
    log "✅ 已备份settings.py"
fi

# 3. 优雅停止服务
log "🛑 优雅停止服务..."

# 停止Celery
if [ ! -z "$CELERY_PIDS" ]; then
    log "🔄 停止Celery进程..."
    echo $CELERY_PIDS | xargs kill -TERM 2>/dev/null || true
    sleep 5
    # 强制杀死仍在运行的进程
    echo $CELERY_PIDS | xargs kill -KILL 2>/dev/null || true
fi

# 停止thworker
if [ ! -z "$THWORKER_PIDS" ]; then
    log "🔄 停止thworker进程..."
    echo $THWORKER_PIDS | xargs kill -TERM 2>/dev/null || true
    sleep 3
fi

# 停止Python服务（发送HUP信号重载，而不是杀死）
if [ ! -z "$PYTHON_PIDS" ]; then
    log "🔄 重载Python服务..."
    echo $PYTHON_PIDS | xargs kill -HUP 2>/dev/null || true
fi

# 重启Docker容器
if [ ! -z "$DOCKER_CONTAINERS" ]; then
    log "🔄 重启Docker容器..."
    echo "$DOCKER_CONTAINERS" | while read container; do
        if [ ! -z "$container" ]; then
            log "  重启容器: $container"
            docker restart "$container" 2>/dev/null || true
        fi
    done
fi

# 4. 等待服务停止
log "⏳ 等待服务完全停止..."
sleep 10

# 5. 检查端口占用
log "🔍 检查端口占用..."
COMMON_PORTS="8000 8001 5555 6379"
for port in $COMMON_PORTS; do
    if netstat -tlnp 2>/dev/null | grep ":$port " > /dev/null; then
        log "⚠️ 端口 $port 仍被占用"
    fi
done

# 6. 启动服务
log "🚀 启动服务..."

cd "$PROJECT_ROOT"

# 检查是否有Docker Compose
if [ -f "docker-compose.yml" ] || [ -f "docker-compose.yaml" ]; then
    log "🐳 使用Docker Compose启动服务..."
    docker-compose up -d 2>/dev/null || docker compose up -d 2>/dev/null || log "❌ Docker Compose启动失败"
elif [ -f "server/manage.py" ]; then
    log "🐍 使用Django直接启动..."
    cd server
    
    # 检查虚拟环境
    if [ -d "venv" ]; then
        source venv/bin/activate
        log "✅ 激活虚拟环境"
    fi
    
    # 启动Django开发服务器（后台运行）
    nohup python manage.py runserver 0.0.0.0:8000 > /tmp/django.log 2>&1 &
    log "✅ Django服务已启动"
    
    # 启动Celery worker（如果配置存在）
    if [ -f "steambase/celery.py" ]; then
        nohup celery -A steambase worker -l info > /tmp/celery.log 2>&1 &
        log "✅ Celery worker已启动"
    fi
    
    cd ..
else
    log "❌ 未找到启动配置"
fi

# 7. 等待服务启动
log "⏳ 等待服务启动..."
sleep 15

# 8. 验证服务状态
log "✅ 验证服务状态..."

# 检查端口
if netstat -tlnp 2>/dev/null | grep ":8000 " > /dev/null; then
    log "✅ Django服务 (8000端口) 正在运行"
else
    log "❌ Django服务未启动"
fi

if pgrep -f "celery" > /dev/null; then
    log "✅ Celery服务正在运行"
else
    log "❌ Celery服务未启动"
fi

# 9. 运行修复脚本
log "🔧 运行房间修复脚本..."
cd "$PROJECT_ROOT"

if [ -f "server/manage.py" ]; then
    cd server
    if [ -d "venv" ]; then
        source venv/bin/activate
    fi
    
    # 运行修复命令
    python manage.py fix_battle_rooms 2>/dev/null || log "⚠️ 修复命令执行失败，可能需要手动运行"
    cd ..
fi

log "✅ 重启和升级完成！"
log "📋 日志文件位置: $LOG_FILE"
log "💡 建议检查以下日志文件:"
log "  - /tmp/django.log"
log "  - /tmp/celery.log"
log "  - Docker容器日志: docker logs <container_name>"
