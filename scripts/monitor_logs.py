#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志监控和分析脚本
"""

import os
import sys
import re
import time
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import argparse

def find_log_files():
    """查找日志文件"""
    log_locations = [
        '/var/log',
        '/tmp',
        '/www/wwwroot/csgoskins.com.cn/logs',
        '/www/wwwroot/csgoskins.com.cn/server/logs',
        '/www/wwwroot/csgoskins.com.cn',
    ]
    
    log_files = []
    log_patterns = [
        '*.log',
        'django*.log',
        'celery*.log',
        'battle*.log',
        'error*.log',
        'access*.log'
    ]
    
    for location in log_locations:
        if os.path.exists(location):
            try:
                for root, dirs, files in os.walk(location):
                    for file in files:
                        if file.endswith('.log'):
                            full_path = os.path.join(root, file)
                            # 检查文件是否可读且不为空
                            try:
                                if os.path.getsize(full_path) > 0:
                                    log_files.append(full_path)
                            except (OSError, IOError):
                                continue
            except (<PERSON>E<PERSON>r, IOError):
                continue
    
    return sorted(set(log_files))

def analyze_battle_logs(log_files, hours=1):
    """分析对战相关日志"""
    
    print(f"🔍 分析最近 {hours} 小时的对战日志...")
    
    # 时间阈值
    threshold = datetime.now() - timedelta(hours=hours)
    
    # 统计数据
    stats = {
        'total_lines': 0,
        'battle_events': 0,
        'errors': 0,
        'warnings': 0,
        'room_events': defaultdict(int),
        'error_types': Counter(),
        'recent_errors': [],
        'async_events': 0,
        'room_states': Counter()
    }
    
    # 正则模式
    patterns = {
        'timestamp': re.compile(r'(\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2})'),
        'battle_room': re.compile(r'room[=\s]([a-zA-Z0-9]+)', re.IGNORECASE),
        'error': re.compile(r'(ERROR|CRITICAL|Exception|Traceback)', re.IGNORECASE),
        'warning': re.compile(r'WARNING', re.IGNORECASE),
        'async': re.compile(r'(异步|async|task)', re.IGNORECASE),
        'room_state': re.compile(r'(满员|运行|结束|取消|可加入)', re.IGNORECASE)
    }
    
    for log_file in log_files:
        try:
            print(f"  📄 分析文件: {log_file}")
            
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    stats['total_lines'] += 1
                    
                    # 检查时间戳
                    timestamp_match = patterns['timestamp'].search(line)
                    if timestamp_match:
                        try:
                            log_time = datetime.strptime(
                                timestamp_match.group(1)[:19], 
                                '%Y-%m-%d %H:%M:%S'
                            )
                            if log_time < threshold:
                                continue  # 跳过过旧的日志
                        except ValueError:
                            pass
                    
                    # 检查对战相关事件
                    if any(keyword in line.lower() for keyword in ['battle', 'room', '对战', '房间']):
                        stats['battle_events'] += 1
                        
                        # 提取房间ID
                        room_match = patterns['battle_room'].search(line)
                        if room_match:
                            room_id = room_match.group(1)
                            stats['room_events'][room_id] += 1
                    
                    # 检查错误
                    if patterns['error'].search(line):
                        stats['errors'] += 1
                        
                        # 提取错误类型
                        if 'Exception' in line:
                            error_type = line.split('Exception')[0].split()[-1] + 'Exception'
                            stats['error_types'][error_type] += 1
                        
                        # 保存最近的错误
                        if len(stats['recent_errors']) < 10:
                            stats['recent_errors'].append({
                                'file': os.path.basename(log_file),
                                'line': line_num,
                                'content': line.strip()[:200]
                            })
                    
                    # 检查警告
                    if patterns['warning'].search(line):
                        stats['warnings'] += 1
                    
                    # 检查异步事件
                    if patterns['async'].search(line):
                        stats['async_events'] += 1
                    
                    # 检查房间状态
                    state_match = patterns['room_state'].search(line)
                    if state_match:
                        state = state_match.group(1)
                        stats['room_states'][state] += 1
        
        except (IOError, OSError) as e:
            print(f"  ❌ 无法读取文件 {log_file}: {e}")
            continue
    
    return stats

def print_analysis_report(stats):
    """打印分析报告"""
    
    print("\n" + "="*60)
    print("📊 对战日志分析报告")
    print("="*60)
    
    # 基本统计
    print(f"📋 基本统计:")
    print(f"  总日志行数: {stats['total_lines']:,}")
    print(f"  对战相关事件: {stats['battle_events']:,}")
    print(f"  异步处理事件: {stats['async_events']:,}")
    print(f"  错误数量: {stats['errors']:,}")
    print(f"  警告数量: {stats['warnings']:,}")
    
    # 房间活动统计
    if stats['room_events']:
        print(f"\n🏠 最活跃的房间 (前10个):")
        for room_id, count in stats['room_events'].most_common(10):
            print(f"  {room_id}: {count} 次事件")
    
    # 房间状态统计
    if stats['room_states']:
        print(f"\n📈 房间状态分布:")
        for state, count in stats['room_states'].most_common():
            print(f"  {state}: {count} 次")
    
    # 错误类型统计
    if stats['error_types']:
        print(f"\n❌ 错误类型分布:")
        for error_type, count in stats['error_types'].most_common():
            print(f"  {error_type}: {count} 次")
    
    # 最近错误
    if stats['recent_errors']:
        print(f"\n🚨 最近的错误 (最多10个):")
        for i, error in enumerate(stats['recent_errors'], 1):
            print(f"  {i}. [{error['file']}:{error['line']}] {error['content']}")
    
    # 健康度评估
    print(f"\n💊 系统健康度评估:")
    
    error_rate = stats['errors'] / max(stats['total_lines'], 1) * 100
    if error_rate < 0.1:
        health = "🟢 良好"
    elif error_rate < 1:
        health = "🟡 一般"
    else:
        health = "🔴 需要关注"
    
    print(f"  错误率: {error_rate:.3f}% - {health}")
    
    if stats['async_events'] > 0:
        print(f"  异步处理: 🟢 正常运行 ({stats['async_events']} 个事件)")
    else:
        print(f"  异步处理: 🟡 未检测到活动")

def tail_logs(log_files, keywords=None):
    """实时监控日志"""
    
    if not keywords:
        keywords = ['battle', 'room', 'error', 'exception', '对战', '房间', '错误']
    
    print(f"🔍 实时监控日志文件 (关键词: {', '.join(keywords)})...")
    print("按 Ctrl+C 停止监控\n")
    
    # 获取文件的当前位置
    file_positions = {}
    for log_file in log_files:
        try:
            with open(log_file, 'r') as f:
                f.seek(0, 2)  # 移动到文件末尾
                file_positions[log_file] = f.tell()
        except (IOError, OSError):
            continue
    
    try:
        while True:
            for log_file in log_files:
                try:
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        # 移动到上次读取的位置
                        f.seek(file_positions.get(log_file, 0))
                        
                        for line in f:
                            # 检查是否包含关键词
                            if any(keyword.lower() in line.lower() for keyword in keywords):
                                timestamp = datetime.now().strftime('%H:%M:%S')
                                filename = os.path.basename(log_file)
                                print(f"[{timestamp}] [{filename}] {line.strip()}")
                        
                        # 更新文件位置
                        file_positions[log_file] = f.tell()
                
                except (IOError, OSError):
                    continue
            
            time.sleep(1)
    
    except KeyboardInterrupt:
        print("\n监控已停止")

def main():
    parser = argparse.ArgumentParser(description="对战日志监控和分析工具")
    parser.add_argument("--analyze", action="store_true", help="分析日志")
    parser.add_argument("--tail", action="store_true", help="实时监控日志")
    parser.add_argument("--hours", type=int, default=1, help="分析最近N小时的日志 (默认: 1)")
    parser.add_argument("--keywords", nargs="+", help="监控的关键词")
    
    args = parser.parse_args()
    
    # 查找日志文件
    log_files = find_log_files()
    
    if not log_files:
        print("❌ 未找到日志文件")
        return
    
    print(f"📁 找到 {len(log_files)} 个日志文件:")
    for log_file in log_files[:10]:  # 只显示前10个
        size = os.path.getsize(log_file) / 1024 / 1024  # MB
        print(f"  {log_file} ({size:.1f} MB)")
    
    if len(log_files) > 10:
        print(f"  ... 还有 {len(log_files) - 10} 个文件")
    
    if args.analyze:
        stats = analyze_battle_logs(log_files, args.hours)
        print_analysis_report(stats)
    
    if args.tail:
        tail_logs(log_files, args.keywords)
    
    if not args.analyze and not args.tail:
        print("\n💡 使用 --analyze 分析日志或 --tail 实时监控")

if __name__ == "__main__":
    main()
