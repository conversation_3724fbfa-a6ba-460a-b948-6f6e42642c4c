#!/bin/bash
# 重启对战相关服务脚本

echo "🔄 重启对战相关服务..."

# 检查并重启Django应用
echo "📋 检查Django进程..."
DJANGO_PIDS=$(ps aux | grep -E "manage.py|wsgi|gunicorn" | grep -v grep | awk '{print $2}')
if [ ! -z "$DJANGO_PIDS" ]; then
    echo "🔄 重启Django进程: $DJANGO_PIDS"
    echo $DJANGO_PIDS | xargs kill -HUP 2>/dev/null || true
    sleep 2
else
    echo "ℹ️ 未发现Django进程"
fi

# 检查并重启Celery worker
echo "📋 检查Celery进程..."
CELERY_PIDS=$(ps aux | grep celery | grep -v grep | awk '{print $2}')
if [ ! -z "$CELERY_PIDS" ]; then
    echo "🔄 重启Celery进程: $CELERY_PIDS"
    echo $CELERY_PIDS | xargs kill -TERM 2>/dev/null || true
    sleep 3
else
    echo "ℹ️ 未发现Celery进程"
fi

# 检查并重启thworker
echo "📋 检查thworker进程..."
THWORKER_PIDS=$(ps aux | grep thworker | grep -v grep | awk '{print $2}')
if [ ! -z "$THWORKER_PIDS" ]; then
    echo "🔄 重启thworker进程: $THWORKER_PIDS"
    echo $THWORKER_PIDS | xargs kill -TERM 2>/dev/null || true
    sleep 3
else
    echo "ℹ️ 未发现thworker进程"
fi

# 检查Docker容器
echo "📋 检查Docker容器..."
if command -v docker &> /dev/null; then
    CONTAINERS=$(docker ps --format "table {{.Names}}" | grep -E "(django|celery|worker)" | grep -v NAMES)
    if [ ! -z "$CONTAINERS" ]; then
        echo "🔄 重启Docker容器:"
        echo "$CONTAINERS" | while read container; do
            echo "  - $container"
            docker restart "$container" 2>/dev/null || true
        done
    else
        echo "ℹ️ 未发现相关Docker容器"
    fi
else
    echo "ℹ️ Docker未安装"
fi

echo "✅ 服务重启完成"
echo ""
echo "💡 建议操作:"
echo "1. 等待30秒让服务完全启动"
echo "2. 运行诊断脚本检查状态: python3 scripts/diagnose_battle_issue.py"
echo "3. 如果问题持续，运行修复脚本: python3 scripts/diagnose_battle_issue.py --fix"
