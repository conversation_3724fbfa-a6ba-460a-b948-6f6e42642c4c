#!/usr/bin/env python
"""
快速测试修复效果
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
django.setup()

def test_imports():
    """测试关键函数是否可以正常导入"""
    try:
        from box.business_room import (
            force_end_stuck_room, 
            check_and_force_end_stuck_rooms,
            acquire_distributed_lock,
            release_distributed_lock,
            DistributedLockContext
        )
        print("✓ 所有关键函数导入成功")
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_room_stats():
    """测试房间统计"""
    try:
        from box.models import CaseRoom, GameState, RoomType
        from django.db.models import Count
        
        stats = CaseRoom.objects.filter(
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        ).values('state').annotate(count=Count('id')).order_by('state')
        
        print("对战房间统计:")
        state_names = {1: 'Initial', 2: 'Joinable', 3: 'Joining', 4: 'Full', 5: 'Running', 11: 'End', 20: 'Cancelled'}
        total = 0
        for stat in stats:
            state_name = state_names.get(stat['state'], f'Unknown({stat["state"]})')
            count = stat['count']
            total += count
            print(f"  状态 {stat['state']} ({state_name}): {count} 个房间")
        
        print(f"总计: {total} 个对战房间")
        return True
    except Exception as e:
        print(f"✗ 统计失败: {e}")
        return False

def main():
    print("开始快速测试...")
    
    tests = [
        ("导入测试", test_imports),
        ("房间统计", test_room_stats),
    ]
    
    passed = 0
    for name, test_func in tests:
        print(f"\n运行 {name}:")
        if test_func():
            passed += 1
        print("-" * 40)
    
    print(f"\n测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 基本功能正常！")
    else:
        print("⚠️ 部分功能异常")

if __name__ == "__main__":
    main()
