#!/usr/bin/env python
"""
测试对战修复效果的脚本
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
sys.path.append('/www/wwwroot/csgoskins.com.cn')
django.setup()

from server.box.models import CaseRoom, GameState, RoomType
from django.utils import timezone
from datetime import timedelta
from django.db.models import Count

def test_room_statistics():
    """测试房间统计功能"""
    print("=== 对战房间统计信息 ===")
    
    try:
        # 统计各状态房间数量
        stats = CaseRoom.objects.filter(
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        ).values('state').annotate(count=Count('id')).order_by('state')
        
        state_names = {
            1: 'Initial',
            2: 'Joinable', 
            3: 'Joining',
            4: 'Full',
            5: 'Running',
            11: 'End',
            20: 'Cancelled'
        }
        
        total_rooms = 0
        for stat in stats:
            state_name = state_names.get(stat['state'], f'Unknown({stat["state"]})')
            count = stat['count']
            total_rooms += count
            print(f'状态 {stat["state"]} ({state_name}): {count} 个房间')
        
        print(f'总计: {total_rooms} 个对战房间')
        
        # 检查可能卡住的房间
        timeout_time = timezone.now() - timedelta(minutes=30)
        stuck_rooms = CaseRoom.objects.filter(
            state__in=[GameState.Full.value, GameState.Running.value],
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=timeout_time
        )
        
        print(f'\n可能卡住的房间 (超过30分钟): {stuck_rooms.count()} 个')
        
        if stuck_rooms.count() > 0:
            print("卡住的房间详情:")
            for room in stuck_rooms[:5]:  # 只显示前5个
                print(f'  房间 {room.short_id}: 状态={room.state}, 更新时间={room.update_time}')
        
        return True
        
    except Exception as e:
        print(f"统计失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_lock_mechanism():
    """测试分布式锁机制"""
    print("\n=== 测试分布式锁机制 ===")
    
    try:
        from server.box.business_room import acquire_distributed_lock, release_distributed_lock
        
        # 测试锁的获取和释放
        test_lock_key = "test_lock_key"
        
        print("测试锁获取...")
        result1 = acquire_distributed_lock(test_lock_key, 10)
        print(f"第一次获取锁: {'成功' if result1 else '失败'}")
        
        if result1:
            # 测试重复获取同一个锁
            result2 = acquire_distributed_lock(test_lock_key, 10)
            print(f"重复获取同一个锁: {'成功' if result2 else '失败'} (应该失败)")
            
            # 释放锁
            release_result = release_distributed_lock(test_lock_key)
            print(f"释放锁: {'成功' if release_result else '失败'}")
            
            # 再次获取锁
            result3 = acquire_distributed_lock(test_lock_key, 10)
            print(f"释放后再次获取锁: {'成功' if result3 else '失败'}")
            
            if result3:
                release_distributed_lock(test_lock_key)
        
        return True
        
    except Exception as e:
        print(f"锁机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_force_end_function():
    """测试强制结束房间功能"""
    print("\n=== 测试强制结束房间功能 ===")
    
    try:
        from server.box.business_room import force_end_stuck_room
        
        # 查找一个可以测试的房间（如果有的话）
        test_room = CaseRoom.objects.filter(
            state__in=[GameState.Full.value, GameState.Running.value],
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        ).first()
        
        if test_room:
            print(f"找到测试房间: {test_room.short_id} (状态: {test_room.state})")
            print("注意: 这只是功能测试，不会实际强制结束房间")
            # 这里不实际调用force_end_stuck_room，只是验证函数存在
            print("强制结束房间功能可用")
        else:
            print("没有找到可测试的房间")
        
        return True
        
    except Exception as e:
        print(f"强制结束功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试对战修复效果...\n")
    
    tests = [
        ("房间统计", test_room_statistics),
        ("分布式锁机制", test_lock_mechanism),
        ("强制结束房间功能", test_force_end_function),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"运行测试: {test_name}")
        try:
            if test_func():
                print(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
        print("-" * 50)
    
    print(f"\n测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复效果良好。")
    else:
        print("⚠️  部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
