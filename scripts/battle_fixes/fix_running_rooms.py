#!/usr/bin/env python
"""
专门修复Running状态卡住的房间
"""
import os
import sys
import django
from django.db import connection, transaction

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
django.setup()

def fix_running_rooms_direct():
    """直接通过SQL修复Running状态的房间"""
    
    print("=== 开始修复Running状态卡住的房间 ===")
    
    with connection.cursor() as cursor:
        # 1. 查找所有Running状态的对战房间
        cursor.execute("""
            SELECT cr.id, cr.short_id, cr.create_time, cr.update_time,
                   TIMESTAMPDIFF(MINUTE, cr.update_time, NOW()) as minutes_stuck
            FROM box_caseroom cr
            WHERE cr.state = 5 AND cr.type IN (1, 3)
            ORDER BY cr.update_time
        """)
        
        running_rooms = cursor.fetchall()
        print(f"发现 {len(running_rooms)} 个Running状态的房间")
        
        if len(running_rooms) == 0:
            print("✓ 没有发现Running状态的房间")
            return
        
        fixed_count = 0
        
        for room_data in running_rooms:
            room_id, short_id, create_time, update_time, minutes_stuck = room_data
            print(f"\n处理房间 {short_id} (卡住 {minutes_stuck} 分钟)...")
            
            try:
                with transaction.atomic():
                    # 检查轮次状态
                    cursor.execute("""
                        SELECT COUNT(*) as total,
                               SUM(CASE WHEN opened = 1 THEN 1 ELSE 0 END) as opened,
                               SUM(CASE WHEN opened = 0 THEN 1 ELSE 0 END) as unopened
                        FROM box_caseroomround
                        WHERE room_id = %s
                    """, [room_id])
                    
                    round_info = cursor.fetchone()
                    total_rounds, opened_rounds, unopened_rounds = round_info or (0, 0, 0)
                    
                    print(f"  轮次状态: 总{total_rounds} 已开{opened_rounds} 未开{unopened_rounds}")
                    
                    # 检查参与者
                    cursor.execute("""
                        SELECT COUNT(*) FROM box_caseroombbet WHERE room_id = %s
                    """, [room_id])
                    bet_count = cursor.fetchone()[0]
                    print(f"  参与者数量: {bet_count}")
                    
                    if bet_count == 0:
                        # 没有参与者，直接取消房间
                        cursor.execute("""
                            UPDATE box_caseroom SET state = 20 WHERE id = %s
                        """, [room_id])
                        print(f"  ✓ 房间无参与者，已取消")
                        fixed_count += 1
                        
                    elif unopened_rounds == 0 and total_rounds > 0:
                        # 所有轮次已完成，应该结束房间
                        print(f"  → 所有轮次已完成，强制结束房间")
                        
                        # 找出获胜者（开箱金额最高的）
                        cursor.execute("""
                            SELECT id, user_id, open_amount 
                            FROM box_caseroombbet 
                            WHERE room_id = %s 
                            ORDER BY open_amount DESC 
                            LIMIT 1
                        """, [room_id])
                        
                        winner_info = cursor.fetchone()
                        if winner_info:
                            winner_bet_id, winner_user_id, winner_amount = winner_info
                            
                            # 重置所有参与者状态
                            cursor.execute("""
                                UPDATE box_caseroombbet 
                                SET victory = 0, win_amount = COALESCE(win_amount, 0), win_items_count = COALESCE(win_items_count, 0)
                                WHERE room_id = %s
                            """, [room_id])
                            
                            # 计算总金额
                            cursor.execute("""
                                SELECT SUM(COALESCE(open_amount, 0)) FROM box_caseroombbet WHERE room_id = %s
                            """, [room_id])
                            total_amount = cursor.fetchone()[0] or 0
                            
                            # 设置获胜者
                            cursor.execute("""
                                UPDATE box_caseroombbet 
                                SET victory = 1, win_amount = %s
                                WHERE id = %s
                            """, [total_amount, winner_bet_id])
                            
                            # 结束房间
                            cursor.execute("""
                                UPDATE box_caseroom SET state = 11 WHERE id = %s
                            """, [room_id])
                            
                            print(f"  ✓ 房间已结束，获胜者用户ID: {winner_user_id}")
                            fixed_count += 1
                        else:
                            # 没有找到参与者，取消房间
                            cursor.execute("""
                                UPDATE box_caseroom SET state = 20 WHERE id = %s
                            """, [room_id])
                            print(f"  ✓ 未找到参与者，房间已取消")
                            fixed_count += 1
                            
                    elif unopened_rounds > 0:
                        # 有未开启的轮次，但长时间没有处理
                        if minutes_stuck > 60:  # 超过1小时
                            print(f"  → 房间卡住超过1小时，强制结束")
                            
                            # 找出当前获胜者
                            cursor.execute("""
                                SELECT id, user_id, open_amount 
                                FROM box_caseroombbet 
                                WHERE room_id = %s 
                                ORDER BY open_amount DESC 
                                LIMIT 1
                            """, [room_id])
                            
                            winner_info = cursor.fetchone()
                            if winner_info:
                                winner_bet_id, winner_user_id, winner_amount = winner_info
                                
                                # 重置所有参与者状态
                                cursor.execute("""
                                    UPDATE box_caseroombbet 
                                    SET victory = 0, win_amount = COALESCE(win_amount, 0), win_items_count = COALESCE(win_items_count, 0)
                                    WHERE room_id = %s
                                """, [room_id])
                                
                                # 计算总金额
                                cursor.execute("""
                                    SELECT SUM(COALESCE(open_amount, 0)) FROM box_caseroombbet WHERE room_id = %s
                                """, [room_id])
                                total_amount = cursor.fetchone()[0] or 0
                                
                                # 设置获胜者
                                cursor.execute("""
                                    UPDATE box_caseroombbet 
                                    SET victory = 1, win_amount = %s
                                    WHERE id = %s
                                """, [total_amount, winner_bet_id])
                                
                                # 结束房间
                                cursor.execute("""
                                    UPDATE box_caseroom SET state = 11 WHERE id = %s
                                """, [room_id])
                                
                                print(f"  ✓ 超时房间已强制结束，获胜者用户ID: {winner_user_id}")
                                fixed_count += 1
                            else:
                                # 取消房间
                                cursor.execute("""
                                    UPDATE box_caseroom SET state = 20 WHERE id = %s
                                """, [room_id])
                                print(f"  ✓ 超时房间已取消")
                                fixed_count += 1
                        else:
                            print(f"  → 房间有未处理轮次但时间不长，暂时保留")
                    
                    else:
                        print(f"  → 房间状态异常，取消处理")
                        cursor.execute("""
                            UPDATE box_caseroom SET state = 20 WHERE id = %s
                        """, [room_id])
                        fixed_count += 1
                        
            except Exception as e:
                print(f"  ✗ 处理房间 {short_id} 失败: {e}")
        
        print(f"\n=== 修复完成 ===")
        print(f"处理了 {len(running_rooms)} 个房间，成功修复 {fixed_count} 个")
        
        # 再次检查状态
        cursor.execute("""
            SELECT COUNT(*) FROM box_caseroom WHERE state = 5 AND type IN (1, 3)
        """)
        remaining_running = cursor.fetchone()[0]
        print(f"剩余Running状态房间: {remaining_running} 个")

if __name__ == "__main__":
    try:
        fix_running_rooms_direct()
        print("\n🎉 修复完成！")
    except Exception as e:
        print(f"\n❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
