#!/usr/bin/env python
"""
立即修复卡住的对战房间
"""
import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
sys.path.append('/www/wwwroot/csgoskins.com.cn/server')
django.setup()

from box.models import CaseRoom, CaseRoomBet, GameState, RoomType
from django.utils import timezone
from django.db import transaction
from django.db.models import Count
import random

def show_stats():
    """显示当前房间统计"""
    print("=== 当前对战房间统计 ===")
    
    stats = CaseRoom.objects.filter(
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
    ).values('state').annotate(count=Count('id')).order_by('state')
    
    state_names = {1: 'Initial', 2: 'Joinable', 3: 'Joining', 4: 'Full', 5: 'Running', 11: 'End', 20: 'Cancelled'}
    total = 0
    
    for stat in stats:
        state_name = state_names.get(stat['state'], f'Unknown({stat["state"]})')
        count = stat['count']
        total += count
        print(f"状态 {stat['state']} ({state_name}): {count} 个房间")
    
    print(f"总计: {total} 个对战房间")
    
    # 检查可能卡住的房间
    timeout_time = timezone.now() - timedelta(minutes=15)
    stuck_rooms = CaseRoom.objects.filter(
        state__in=[GameState.Full.value, GameState.Running.value],
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=timeout_time
    )
    
    print(f"\n可能卡住的房间 (超过15分钟): {stuck_rooms.count()} 个")
    return stuck_rooms

def force_end_room_simple(room):
    """简单的强制结束房间"""
    try:
        with transaction.atomic():
            # 重新获取房间
            room = CaseRoom.objects.select_for_update().get(uid=room.uid)
            
            if room.state in [GameState.End.value, GameState.Cancelled.value]:
                print(f"  房间 {room.short_id} 已经结束，跳过")
                return True
            
            bets = CaseRoomBet.objects.filter(room=room)
            
            if bets.count() == 0:
                # 没有参与者，直接取消
                room.state = GameState.Cancelled.value
                room.save()
                print(f"  房间 {room.short_id} 无参与者，已取消")
                return True
            
            # 有参与者，选择获胜者
            sorted_bets = bets.order_by('-open_amount')
            winner = sorted_bets[0]
            
            # 如果有平局，随机选择
            if len(sorted_bets) >= 2 and sorted_bets[0].open_amount == sorted_bets[1].open_amount:
                tied_bets = [bet for bet in sorted_bets if bet.open_amount == sorted_bets[0].open_amount]
                winner = random.choice(tied_bets)
            
            # 重置所有参与者状态
            for bet in bets:
                bet.victory = 0
                if bet.win_amount is None:
                    bet.win_amount = 0
                if bet.win_items_count is None:
                    bet.win_items_count = 0
                bet.save()
            
            # 设置获胜者
            winner.victory = 1
            total_amount = sum(bet.open_amount or 0 for bet in bets)
            winner.win_amount = total_amount
            winner.save()
            
            # 更新房间状态
            room.state = GameState.End.value
            room.save()
            
            print(f"  房间 {room.short_id} 强制结束，获胜者: {winner.user.username}")
            return True
            
    except Exception as e:
        print(f"  房间 {room.short_id} 强制结束失败: {e}")
        try:
            # 最后的降级方案
            room.state = GameState.Cancelled.value
            room.save()
            print(f"  房间 {room.short_id} 已取消")
        except:
            pass
        return False

def fix_stuck_rooms():
    """修复卡住的房间"""
    stuck_rooms = show_stats()
    
    if stuck_rooms.count() == 0:
        print("\n✓ 没有发现卡住的房间")
        return
    
    print(f"\n开始修复 {stuck_rooms.count()} 个卡住的房间...")
    
    success_count = 0
    for room in stuck_rooms:
        print(f"处理房间 {room.short_id} (状态: {room.state}, 更新时间: {room.update_time})")
        if force_end_room_simple(room):
            success_count += 1
    
    print(f"\n修复完成: 成功 {success_count}/{stuck_rooms.count()} 个房间")
    
    # 再次显示统计
    print("\n=== 修复后统计 ===")
    show_stats()

if __name__ == "__main__":
    print("开始修复卡住的对战房间...")
    print(f"当前时间: {datetime.now()}")
    
    try:
        fix_stuck_rooms()
        print("\n🎉 修复完成！")
    except Exception as e:
        print(f"\n❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
