#!/usr/bin/env python
"""
直接查询卡住的对战状态
"""
import mysql.connector
import os
from datetime import datetime, timedelta

# 数据库配置 - 请根据实际情况修改
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',  # 请修改为实际用户名
    'password': '',  # 请修改为实际密码
    'database': 'csgoskins'  # 请修改为实际数据库名
}

def check_stuck_battles():
    """检查卡住的对战状态"""
    try:
        # 连接数据库
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("=== 对战房间状态统计 ===")
        
        # 统计各状态房间数量
        query = """
        SELECT state, COUNT(*) as count 
        FROM box_caseroom 
        WHERE type IN (1, 3) 
        GROUP BY state 
        ORDER BY state
        """
        cursor.execute(query)
        results = cursor.fetchall()
        
        state_names = {1: 'Initial', 2: 'Joinable', 3: 'Joining', 4: 'Full', 5: 'Running', 11: 'End', 20: 'Cancelled'}
        total = 0
        
        for state, count in results:
            state_name = state_names.get(state, f'Unknown({state})')
            print(f"状态 {state} ({state_name}): {count} 个房间")
            total += count
        
        print(f"总计: {total} 个对战房间")
        
        # 查看Running状态的房间详情
        print("\n=== Running状态房间详情 ===")
        query = """
        SELECT short_id, state, type, create_time, update_time, max_joiner
        FROM box_caseroom 
        WHERE state = 5 AND type IN (1, 3)
        ORDER BY update_time
        LIMIT 10
        """
        cursor.execute(query)
        running_rooms = cursor.fetchall()
        
        print(f"Running状态房间数量: {len(running_rooms)}")
        
        for room in running_rooms:
            short_id, state, room_type, create_time, update_time, max_joiner = room
            print(f"\n房间 {short_id}:")
            print(f"  状态: {state}")
            print(f"  类型: {room_type}")
            print(f"  创建时间: {create_time}")
            print(f"  更新时间: {update_time}")
            print(f"  最大参与者: {max_joiner}")
            
            # 查看参与者数量
            cursor.execute("SELECT COUNT(*) FROM box_caseroombbet WHERE room_id = (SELECT id FROM box_caseroom WHERE short_id = %s)", (short_id,))
            bet_count = cursor.fetchone()[0]
            print(f"  参与者数量: {bet_count}")
            
            # 查看轮次情况
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_rounds,
                    SUM(CASE WHEN opened = 1 THEN 1 ELSE 0 END) as opened_rounds,
                    SUM(CASE WHEN opened = 0 THEN 1 ELSE 0 END) as unopened_rounds
                FROM box_caseroomround 
                WHERE room_id = (SELECT id FROM box_caseroom WHERE short_id = %s)
            """, (short_id,))
            round_info = cursor.fetchone()
            if round_info:
                total_rounds, opened_rounds, unopened_rounds = round_info
                print(f"  总轮次: {total_rounds}")
                print(f"  已开启轮次: {opened_rounds}")
                print(f"  未开启轮次: {unopened_rounds}")
                
                # 判断卡住原因
                if unopened_rounds == 0:
                    print(f"  ❌ 问题: 所有轮次已开启但房间仍在Running状态")
                elif unopened_rounds > 0:
                    print(f"  ⚠️  问题: 有{unopened_rounds}个轮次未开启，可能卡在轮次处理")
        
        # 查看长时间Running的房间
        print("\n=== 长时间Running的房间 ===")
        thirty_min_ago = datetime.now() - timedelta(minutes=30)
        query = """
        SELECT short_id, update_time, TIMESTAMPDIFF(MINUTE, update_time, NOW()) as minutes_stuck
        FROM box_caseroom 
        WHERE state = 5 AND type IN (1, 3) AND update_time < %s
        ORDER BY update_time
        """
        cursor.execute(query, (thirty_min_ago,))
        stuck_rooms = cursor.fetchall()
        
        print(f"超过30分钟的Running房间: {len(stuck_rooms)} 个")
        for room in stuck_rooms[:5]:
            short_id, update_time, minutes_stuck = room
            print(f"  房间 {short_id}: 卡住 {minutes_stuck} 分钟 (最后更新: {update_time})")
        
        cursor.close()
        conn.close()
        
    except mysql.connector.Error as e:
        print(f"数据库连接错误: {e}")
        print("请检查数据库配置信息")
    except Exception as e:
        print(f"查询错误: {e}")

if __name__ == "__main__":
    print("开始检查卡住的对战状态...")
    check_stuck_battles()
    print("\n检查完成！")
