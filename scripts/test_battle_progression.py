#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试对战推进修复效果
"""

import os
import sys
import time
from datetime import datetime

# 设置Django环境
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'server'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

from django.utils import timezone
from box.models import CaseRoom, GameState, RoomType
from box.business_room import ready_to_run_room, run_battle_room

def test_battle_progression():
    """测试对战推进功能"""
    
    print("🧪 测试对战推进修复效果")
    print("=" * 50)
    
    # 1. 查找测试房间
    test_rooms = CaseRoom.objects.filter(
        state__in=[GameState.Full.value, GameState.Running.value],
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
    ).order_by('-create_time')[:3]  # 取最新的3个房间
    
    if not test_rooms.exists():
        print("❌ 没有找到可测试的房间")
        print("💡 请先创建一些对战房间进行测试")
        return
    
    print(f"🔍 找到 {test_rooms.count()} 个测试房间:")
    
    for room in test_rooms:
        bets_count = room.bets.count()
        print(f"  房间 {room.short_id}: 状态={room.state}, 参与者={bets_count}/{room.max_joiner}")
    
    # 2. 测试ready_to_run_room函数
    print(f"\n🧪 测试 ready_to_run_room 函数:")
    
    for room in test_rooms:
        try:
            print(f"\n  测试房间 {room.short_id}:")
            print(f"    - 调用前状态: {room.state}")
            
            # 调用ready_to_run_room
            ready_to_run_room(room.uid)
            
            # 重新获取房间状态
            room.refresh_from_db()
            print(f"    - 调用后状态: {room.state}")
            
            # 检查是否有未开启的轮次
            unopened_rounds = room.rounds.filter(opened=False).count()
            print(f"    - 未开启轮次: {unopened_rounds}")
            
        except Exception as e:
            print(f"    ❌ 测试失败: {e}")
    
    # 3. 测试run_battle_room函数
    print(f"\n🧪 测试 run_battle_room 函数:")
    
    running_rooms = CaseRoom.objects.filter(
        state=GameState.Running.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
    )[:2]  # 取2个运行中的房间
    
    if running_rooms.exists():
        for room in running_rooms:
            try:
                print(f"\n  测试房间 {room.short_id}:")
                
                # 检查未开启的轮次
                unopened_rounds = room.rounds.filter(opened=False)
                print(f"    - 未开启轮次数: {unopened_rounds.count()}")
                
                if unopened_rounds.exists():
                    # 调用run_battle_room
                    run_battle_room(room.uid)
                    
                    # 重新检查
                    room.refresh_from_db()
                    new_unopened = room.rounds.filter(opened=False).count()
                    print(f"    - 处理后未开启轮次: {new_unopened}")
                    
                    if new_unopened < unopened_rounds.count():
                        print(f"    ✅ 成功处理了轮次")
                    else:
                        print(f"    ⚠️ 轮次未被处理")
                else:
                    print(f"    ℹ️ 所有轮次已完成")
                    
            except Exception as e:
                print(f"    ❌ 测试失败: {e}")
    else:
        print("  ℹ️ 没有运行中的房间可测试")
    
    # 4. 检查日志输出
    print(f"\n📋 检查修复效果:")
    print("  - 查看Django日志中是否有以下信息:")
    print("    * 'Room XXX is full, proceeding with battle'")
    print("    * 'Using fallback logic for room XXX'")
    print("    * 'Battle room XXX first round started with fallback logic'")
    print("    * 'Processing room XXX (state=X) with lock'")
    print("    * 'run_battle_room: Processing room XXX'")
    
    print(f"\n💡 建议:")
    print("  1. 观察日志输出，确认修复逻辑被执行")
    print("  2. 检查房间状态是否正确转换")
    print("  3. 验证轮次是否正常推进")
    print("  4. 监控新创建的房间是否正常工作")

def monitor_room_changes():
    """监控房间状态变化"""
    
    print("\n🔍 监控房间状态变化 (按Ctrl+C停止):")
    
    # 记录初始状态
    initial_states = {}
    rooms = CaseRoom.objects.filter(
        state__in=[GameState.Full.value, GameState.Running.value],
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
    )
    
    for room in rooms:
        initial_states[room.uid] = {
            'state': room.state,
            'unopened_rounds': room.rounds.filter(opened=False).count()
        }
    
    print(f"  初始监控 {len(initial_states)} 个房间")
    
    try:
        for i in range(30):  # 监控30次，每次5秒
            time.sleep(5)
            
            changes_detected = False
            current_rooms = CaseRoom.objects.filter(uid__in=initial_states.keys())
            
            for room in current_rooms:
                initial = initial_states[room.uid]
                current_unopened = room.rounds.filter(opened=False).count()
                
                if (room.state != initial['state'] or 
                    current_unopened != initial['unopened_rounds']):
                    
                    print(f"  🔄 房间 {room.short_id} 变化:")
                    print(f"    状态: {initial['state']} -> {room.state}")
                    print(f"    未开启轮次: {initial['unopened_rounds']} -> {current_unopened}")
                    
                    # 更新记录
                    initial_states[room.uid] = {
                        'state': room.state,
                        'unopened_rounds': current_unopened
                    }
                    changes_detected = True
            
            if not changes_detected and i % 6 == 0:  # 每30秒输出一次状态
                print(f"  ⏰ {datetime.now().strftime('%H:%M:%S')} - 监控中...")
    
    except KeyboardInterrupt:
        print(f"\n  监控已停止")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="测试对战推进修复效果")
    parser.add_argument("--monitor", action="store_true", help="监控房间状态变化")
    
    args = parser.parse_args()
    
    test_battle_progression()
    
    if args.monitor:
        monitor_room_changes()
