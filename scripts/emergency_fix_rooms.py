#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复卡住的房间
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'server'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

from django.utils import timezone
from django.db import transaction
from box.models import CaseRoom, CaseRoomBet, GameState, RoomType

def emergency_fix():
    """紧急修复卡住的房间"""
    
    print("🚨 开始紧急修复卡住的房间...")
    print("=" * 60)
    
    # 1. 显示当前状态
    print("📊 当前房间状态:")
    states = {
        GameState.Joinable.value: "可加入",
        GameState.Full.value: "满员", 
        GameState.Running.value: "进行中",
        GameState.End.value: "已结束",
        GameState.Cancelled.value: "已取消"
    }
    
    total_rooms = 0
    for state_value, state_name in states.items():
        count = CaseRoom.objects.filter(
            state=state_value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        ).count()
        if count > 0:
            print(f"  {state_name}: {count} 个")
            total_rooms += count
    
    print(f"  总计: {total_rooms} 个对战房间")
    
    # 2. 修复卡住的满员房间（超过2分钟）
    threshold_time = timezone.now() - timedelta(minutes=2)
    stuck_full_rooms = CaseRoom.objects.filter(
        state=GameState.Full.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=threshold_time
    )
    
    print(f"\n🔍 发现 {stuck_full_rooms.count()} 个卡住的满员房间")
    
    fixed_full = 0
    for room in stuck_full_rooms:
        try:
            with transaction.atomic():
                room = CaseRoom.objects.select_for_update().get(id=room.id)
                bets_count = room.bets.count()
                
                print(f"🔧 修复房间 {room.short_id}: {bets_count}/{room.max_joiner} 参与者")
                
                if bets_count == room.max_joiner:
                    # 满员房间，设置为运行状态
                    room.state = GameState.Running.value
                    room.save()
                    print(f"  ✅ 设置为运行状态")
                    fixed_full += 1
                elif bets_count > 0:
                    # 有参与者但未满员，重置为可加入状态
                    room.state = GameState.Joinable.value
                    room.save()
                    print(f"  🔄 重置为可加入状态")
                    fixed_full += 1
                else:
                    # 无参与者，取消房间
                    room.state = GameState.Cancelled.value
                    room.save()
                    print(f"  ❌ 取消房间（无参与者）")
                    fixed_full += 1
                    
        except Exception as e:
            print(f"  ❌ 修复房间 {room.short_id} 失败: {e}")
    
    # 3. 修复长时间运行的房间（超过30分钟）
    long_running_threshold = timezone.now() - timedelta(minutes=30)
    stuck_running_rooms = CaseRoom.objects.filter(
        state=GameState.Running.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=long_running_threshold
    )
    
    print(f"\n🔍 发现 {stuck_running_rooms.count()} 个长时间运行的房间")
    
    fixed_running = 0
    for room in stuck_running_rooms:
        try:
            with transaction.atomic():
                room = CaseRoom.objects.select_for_update().get(id=room.id)
                
                time_diff = timezone.now() - room.update_time
                print(f"🔧 修复房间 {room.short_id}: 运行了 {time_diff.total_seconds()/60:.1f} 分钟")
                
                # 设置为结束状态
                room.state = GameState.End.value
                room.save()
                print(f"  ✅ 设置为结束状态")
                fixed_running += 1
                    
        except Exception as e:
            print(f"  ❌ 修复房间 {room.short_id} 失败: {e}")
    
    # 4. 显示修复后的状态
    print(f"\n📊 修复后房间状态:")
    total_after = 0
    for state_value, state_name in states.items():
        count = CaseRoom.objects.filter(
            state=state_value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        ).count()
        if count > 0:
            print(f"  {state_name}: {count} 个")
            total_after += count
    
    print(f"  总计: {total_after} 个对战房间")
    
    print(f"\n✅ 紧急修复完成:")
    print(f"  - 修复满员房间: {fixed_full} 个")
    print(f"  - 修复运行房间: {fixed_running} 个")
    print(f"  - 总计修复: {fixed_full + fixed_running} 个")
    
    # 5. 检查是否还有卡住的房间
    remaining_stuck = CaseRoom.objects.filter(
        state=GameState.Full.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=threshold_time
    ).count()
    
    if remaining_stuck > 0:
        print(f"\n⚠️ 仍有 {remaining_stuck} 个房间卡住，建议重新运行此脚本")
    else:
        print(f"\n🎉 所有卡住的房间已修复完成！")

def batch_fix_all():
    """批量修复所有问题房间"""
    
    print("🚨 批量修复所有问题房间...")
    
    # 修复所有卡住的满员房间（不限时间）
    all_stuck_full = CaseRoom.objects.filter(
        state=GameState.Full.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
    )
    
    print(f"🔍 发现 {all_stuck_full.count()} 个满员房间")
    
    fixed_count = 0
    for room in all_stuck_full:
        try:
            with transaction.atomic():
                room = CaseRoom.objects.select_for_update().get(id=room.id)
                bets_count = room.bets.count()
                
                if bets_count == room.max_joiner:
                    room.state = GameState.Running.value
                    room.save()
                    fixed_count += 1
                elif bets_count > 0:
                    room.state = GameState.Joinable.value
                    room.save()
                    fixed_count += 1
                else:
                    room.state = GameState.Cancelled.value
                    room.save()
                    fixed_count += 1
                    
        except Exception as e:
            print(f"修复房间 {room.short_id} 失败: {e}")
    
    print(f"✅ 批量修复完成: {fixed_count} 个房间")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="紧急修复卡住的房间")
    parser.add_argument("--all", action="store_true", help="修复所有满员房间")
    
    args = parser.parse_args()
    
    if args.all:
        batch_fix_all()
    else:
        emergency_fix()
