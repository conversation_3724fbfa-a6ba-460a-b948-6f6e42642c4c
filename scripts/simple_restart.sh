#!/bin/bash
# 简化的重启脚本

echo "🔄 开始重启服务..."

# 查找并重启Python进程
echo "📋 查找Python服务进程..."
PYTHON_PIDS=$(pgrep -f "python.*manage.py\|python.*gunicorn\|python.*uwsgi" 2>/dev/null || true)

if [ ! -z "$PYTHON_PIDS" ]; then
    echo "🔄 重启Python服务进程: $PYTHON_PIDS"
    echo $PYTHON_PIDS | xargs kill -HUP 2>/dev/null || true
    sleep 3
else
    echo "ℹ️ 未发现Python服务进程"
fi

# 查找并重启Celery进程
echo "📋 查找Celery进程..."
CELERY_PIDS=$(pgrep -f "celery" 2>/dev/null || true)

if [ ! -z "$CELERY_PIDS" ]; then
    echo "🔄 重启Celery进程: $CELERY_PIDS"
    echo $CELERY_PIDS | xargs kill -TERM 2>/dev/null || true
    sleep 5
else
    echo "ℹ️ 未发现Celery进程"
fi

# 检查Docker容器
echo "📋 检查Docker容器..."
if command -v docker &> /dev/null; then
    CONTAINERS=$(docker ps --format "{{.Names}}" 2>/dev/null | grep -E "(django|celery|worker|web)" || true)
    if [ ! -z "$CONTAINERS" ]; then
        echo "🔄 重启Docker容器:"
        echo "$CONTAINERS" | while read container; do
            if [ ! -z "$container" ]; then
                echo "  重启: $container"
                docker restart "$container" 2>/dev/null || true
            fi
        done
    else
        echo "ℹ️ 未发现相关Docker容器"
    fi
else
    echo "ℹ️ Docker未安装"
fi

echo "✅ 重启完成"
echo "💡 等待30秒让服务完全启动..."
sleep 30

echo "🔍 检查服务状态..."
if netstat -tlnp 2>/dev/null | grep ":8000 " > /dev/null; then
    echo "✅ Django服务 (8000端口) 正在运行"
else
    echo "❌ Django服务未在8000端口运行"
fi

if pgrep -f "celery" > /dev/null; then
    echo "✅ Celery服务正在运行"
else
    echo "❌ Celery服务未运行"
fi

echo "🎯 重启完成！"
