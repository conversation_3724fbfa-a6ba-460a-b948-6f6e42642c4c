#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析对战推进日志，找出问题根源
"""

import os
import re
import sys
from datetime import datetime, timedelta
from collections import defaultdict, Counter

def find_log_files():
    """查找日志文件"""
    log_locations = [
        '/var/log',
        '/tmp',
        '/www/wwwroot/csgoskins.com.cn/logs',
        '/www/wwwroot/csgoskins.com.cn/server/logs',
        '/www/wwwroot/csgoskins.com.cn',
        '/home',
        '.'
    ]
    
    log_files = []
    for location in log_locations:
        if os.path.exists(location):
            try:
                for root, dirs, files in os.walk(location):
                    for file in files:
                        if file.endswith('.log') and any(keyword in file.lower() for keyword in ['django', 'celery', 'battle', 'error', 'debug']):
                            full_path = os.path.join(root, file)
                            try:
                                if os.path.getsize(full_path) > 0:
                                    log_files.append(full_path)
                            except (<PERSON>E<PERSON>r, IOError):
                                continue
            except (OSError, IOError):
                continue
    
    return sorted(set(log_files))

def analyze_battle_progression_issues(log_files, hours=2):
    """分析对战推进问题"""
    
    print(f"🔍 分析最近 {hours} 小时的对战推进问题...")
    
    # 时间阈值
    threshold = datetime.now() - timedelta(hours=hours)
    
    # 统计数据
    issues = {
        'import_errors': [],
        'async_failures': [],
        'room_processing_errors': [],
        'lock_conflicts': [],
        'database_errors': [],
        'round_processing_issues': [],
        'state_inconsistencies': [],
        'celery_task_failures': []
    }
    
    # 关键模式
    patterns = {
        'timestamp': re.compile(r'(\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2})'),
        'room_id': re.compile(r'room[=\s]([a-zA-Z0-9]+)', re.IGNORECASE),
        'import_error': re.compile(r'(ImportError|ModuleNotFoundError|No module named)', re.IGNORECASE),
        'async_error': re.compile(r'(async.*fail|异步.*失败|AsyncBattleProgressionManager)', re.IGNORECASE),
        'lock_error': re.compile(r'(lock.*fail|锁.*失败|acquire.*lock)', re.IGNORECASE),
        'db_error': re.compile(r'(database|connection|mysql|postgresql)', re.IGNORECASE),
        'round_error': re.compile(r'(round.*fail|轮次.*失败|room_round)', re.IGNORECASE),
        'state_error': re.compile(r'(state.*inconsist|状态.*不一致|GameState)', re.IGNORECASE),
        'celery_error': re.compile(r'(celery.*fail|任务.*失败|thworker)', re.IGNORECASE)
    }
    
    room_activity = defaultdict(list)
    error_timeline = []
    
    for log_file in log_files:
        try:
            print(f"  📄 分析文件: {os.path.basename(log_file)}")
            
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    # 检查时间戳
                    timestamp_match = patterns['timestamp'].search(line)
                    if timestamp_match:
                        try:
                            log_time = datetime.strptime(
                                timestamp_match.group(1)[:19], 
                                '%Y-%m-%d %H:%M:%S'
                            )
                            if log_time < threshold:
                                continue
                        except ValueError:
                            pass
                    
                    # 提取房间ID
                    room_match = patterns['room_id'].search(line)
                    room_id = room_match.group(1) if room_match else 'unknown'
                    
                    # 检查各种错误类型
                    if patterns['import_error'].search(line):
                        issues['import_errors'].append({
                            'file': os.path.basename(log_file),
                            'line': line_num,
                            'room': room_id,
                            'content': line.strip()[:200]
                        })
                        error_timeline.append(('import_error', log_time if 'log_time' in locals() else datetime.now()))
                    
                    if patterns['async_error'].search(line):
                        issues['async_failures'].append({
                            'file': os.path.basename(log_file),
                            'line': line_num,
                            'room': room_id,
                            'content': line.strip()[:200]
                        })
                        error_timeline.append(('async_error', log_time if 'log_time' in locals() else datetime.now()))
                    
                    if patterns['lock_error'].search(line):
                        issues['lock_conflicts'].append({
                            'file': os.path.basename(log_file),
                            'line': line_num,
                            'room': room_id,
                            'content': line.strip()[:200]
                        })
                    
                    if patterns['db_error'].search(line) and 'error' in line.lower():
                        issues['database_errors'].append({
                            'file': os.path.basename(log_file),
                            'line': line_num,
                            'room': room_id,
                            'content': line.strip()[:200]
                        })
                    
                    if patterns['round_error'].search(line):
                        issues['round_processing_issues'].append({
                            'file': os.path.basename(log_file),
                            'line': line_num,
                            'room': room_id,
                            'content': line.strip()[:200]
                        })
                    
                    if patterns['state_error'].search(line):
                        issues['state_inconsistencies'].append({
                            'file': os.path.basename(log_file),
                            'line': line_num,
                            'room': room_id,
                            'content': line.strip()[:200]
                        })
                    
                    if patterns['celery_error'].search(line):
                        issues['celery_task_failures'].append({
                            'file': os.path.basename(log_file),
                            'line': line_num,
                            'room': room_id,
                            'content': line.strip()[:200]
                        })
                    
                    # 记录房间活动
                    if room_id != 'unknown' and any(keyword in line.lower() for keyword in ['processing', 'executing', 'room', '房间']):
                        room_activity[room_id].append({
                            'time': log_time if 'log_time' in locals() else datetime.now(),
                            'action': line.strip()[:100]
                        })
        
        except (IOError, OSError) as e:
            print(f"  ❌ 无法读取文件 {log_file}: {e}")
            continue
    
    return issues, room_activity, error_timeline

def print_analysis_report(issues, room_activity, error_timeline):
    """打印分析报告"""
    
    print("\n" + "="*80)
    print("🔍 对战推进问题分析报告")
    print("="*80)
    
    # 1. 错误统计
    print("\n📊 错误类型统计:")
    total_errors = 0
    for error_type, error_list in issues.items():
        count = len(error_list)
        if count > 0:
            total_errors += count
            print(f"  {error_type.replace('_', ' ').title()}: {count} 个")
    
    print(f"  总错误数: {total_errors}")
    
    # 2. 最严重的问题
    print(f"\n🚨 最严重的问题:")
    
    # 导入错误
    if issues['import_errors']:
        print(f"  ❌ 模块导入失败 ({len(issues['import_errors'])} 个):")
        for error in issues['import_errors'][:3]:
            print(f"    - {error['content'][:100]}...")
    
    # 异步系统失败
    if issues['async_failures']:
        print(f"  ❌ 异步系统失败 ({len(issues['async_failures'])} 个):")
        for error in issues['async_failures'][:3]:
            print(f"    - {error['content'][:100]}...")
    
    # 数据库错误
    if issues['database_errors']:
        print(f"  ❌ 数据库错误 ({len(issues['database_errors'])} 个):")
        for error in issues['database_errors'][:3]:
            print(f"    - {error['content'][:100]}...")
    
    # Celery任务失败
    if issues['celery_task_failures']:
        print(f"  ❌ Celery任务失败 ({len(issues['celery_task_failures'])} 个):")
        for error in issues['celery_task_failures'][:3]:
            print(f"    - {error['content'][:100]}...")
    
    # 3. 房间活动分析
    if room_activity:
        print(f"\n🏠 房间活动分析 (前10个最活跃房间):")
        sorted_rooms = sorted(room_activity.items(), key=lambda x: len(x[1]), reverse=True)
        for room_id, activities in sorted_rooms[:10]:
            print(f"  房间 {room_id}: {len(activities)} 次活动")
            if activities:
                latest = max(activities, key=lambda x: x['time'])
                print(f"    最新活动: {latest['action'][:80]}...")
    
    # 4. 错误时间线
    if error_timeline:
        print(f"\n⏰ 错误时间线 (最近10个):")
        sorted_errors = sorted(error_timeline, key=lambda x: x[1], reverse=True)
        for error_type, error_time in sorted_errors[:10]:
            print(f"  {error_time.strftime('%H:%M:%S')} - {error_type}")
    
    # 5. 问题诊断和建议
    print(f"\n💡 问题诊断和建议:")
    
    if issues['import_errors']:
        print("  🔧 模块导入问题:")
        print("    - 检查异步模块是否正确安装")
        print("    - 验证Python路径和依赖")
        print("    - 考虑使用降级模式")
    
    if issues['async_failures']:
        print("  🔧 异步系统问题:")
        print("    - 异步处理器可能未正确初始化")
        print("    - 检查事件循环状态")
        print("    - 考虑重启异步服务")
    
    if issues['database_errors']:
        print("  🔧 数据库问题:")
        print("    - 检查数据库连接池")
        print("    - 验证事务处理")
        print("    - 考虑增加连接超时")
    
    if issues['celery_task_failures']:
        print("  🔧 Celery任务问题:")
        print("    - 检查Celery worker状态")
        print("    - 验证任务队列")
        print("    - 考虑重启Celery服务")
    
    if total_errors == 0:
        print("  ✅ 未发现明显的错误模式")
        print("  💡 建议检查:")
        print("    - 房间状态是否正确更新")
        print("    - 定时任务是否正常运行")
        print("    - 数据库事务是否正确提交")

def main():
    print("🔍 对战推进问题分析工具")
    print("=" * 50)
    
    # 查找日志文件
    log_files = find_log_files()
    
    if not log_files:
        print("❌ 未找到日志文件")
        print("💡 请检查以下位置是否有日志文件:")
        print("  - /var/log/")
        print("  - /www/wwwroot/csgoskins.com.cn/logs/")
        print("  - /www/wwwroot/csgoskins.com.cn/server/logs/")
        return
    
    print(f"📁 找到 {len(log_files)} 个日志文件:")
    for log_file in log_files[:5]:  # 只显示前5个
        size = os.path.getsize(log_file) / 1024 / 1024  # MB
        print(f"  {log_file} ({size:.1f} MB)")
    
    if len(log_files) > 5:
        print(f"  ... 还有 {len(log_files) - 5} 个文件")
    
    # 分析日志
    issues, room_activity, error_timeline = analyze_battle_progression_issues(log_files, hours=2)
    
    # 打印报告
    print_analysis_report(issues, room_activity, error_timeline)

if __name__ == "__main__":
    main()
