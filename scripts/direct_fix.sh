#!/bin/bash
# 直接修复卡住的房间

echo "🚨 直接修复卡住的对战房间..."

# 检查数据库连接
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="csgoskins"
DB_USER="root"

# 尝试不同的数据库配置
if [ -f "/www/wwwroot/csgoskins.com.cn/server/steambase/settings.py" ]; then
    echo "📋 从Django配置读取数据库信息..."
    
    # 提取数据库配置
    DB_INFO=$(python3 -c "
import sys, os
sys.path.insert(0, '/www/wwwroot/csgoskins.com.cn/server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
try:
    from django.conf import settings
    db = settings.DATABASES['default']
    print(f\"{db.get('HOST', 'localhost')}:{db.get('PORT', '3306')}:{db.get('NAME', 'csgoskins')}:{db.get('USER', 'root')}\")
except:
    print('localhost:3306:csgoskins:root'
" 2>/dev/null || echo "localhost:3306:csgoskins:root")
    
    IFS=':' read -r DB_HOST DB_PORT DB_NAME DB_USER <<< "$DB_INFO"
fi

echo "🔍 数据库连接信息: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"

# 创建临时SQL文件
TEMP_SQL="/tmp/fix_rooms_$(date +%s).sql"

cat > "$TEMP_SQL" << 'EOF'
-- 查看当前房间状态
SELECT '=== 当前房间状态分布 ===' as info;
SELECT 
    state,
    COUNT(*) as count,
    CASE 
        WHEN state = 1 THEN '初始'
        WHEN state = 2 THEN '可加入'
        WHEN state = 3 THEN '加入中'
        WHEN state = 4 THEN '满员'
        WHEN state = 5 THEN '进行中'
        WHEN state = 6 THEN '已结束'
        WHEN state = 7 THEN '已取消'
        ELSE '未知'
    END as state_name
FROM box_caseroom 
WHERE type IN (1, 3)
GROUP BY state
ORDER BY state;

-- 查看卡住的满员房间
SELECT '=== 卡住的满员房间 ===' as info;
SELECT 
    short_id,
    state,
    max_joiner,
    update_time,
    TIMESTAMPDIFF(MINUTE, update_time, NOW()) as minutes_stuck
FROM box_caseroom 
WHERE state = 4
  AND type IN (1, 3)
  AND update_time < DATE_SUB(NOW(), INTERVAL 2 MINUTE)
ORDER BY update_time
LIMIT 10;

-- 修复卡住的满员房间
SELECT '=== 修复卡住的满员房间 ===' as info;
UPDATE box_caseroom 
SET 
    state = 5,
    update_time = NOW()
WHERE state = 4
  AND type IN (1, 3)
  AND update_time < DATE_SUB(NOW(), INTERVAL 2 MINUTE);

SELECT ROW_COUNT() as '修复的满员房间数量';

-- 修复长时间运行的房间
SELECT '=== 修复长时间运行的房间 ===' as info;
UPDATE box_caseroom 
SET 
    state = 6,
    update_time = NOW()
WHERE state = 5
  AND type IN (1, 3)
  AND update_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE);

SELECT ROW_COUNT() as '修复的运行房间数量';

-- 显示修复后状态
SELECT '=== 修复后房间状态分布 ===' as info;
SELECT 
    state,
    COUNT(*) as count,
    CASE 
        WHEN state = 1 THEN '初始'
        WHEN state = 2 THEN '可加入'
        WHEN state = 3 THEN '加入中'
        WHEN state = 4 THEN '满员'
        WHEN state = 5 THEN '进行中'
        WHEN state = 6 THEN '已结束'
        WHEN state = 7 THEN '已取消'
        ELSE '未知'
    END as state_name
FROM box_caseroom 
WHERE type IN (1, 3)
GROUP BY state
ORDER BY state;
EOF

echo "📝 SQL脚本已创建: $TEMP_SQL"

# 尝试执行SQL
echo "🔧 执行修复SQL..."

# 尝试不同的MySQL客户端
if command -v mysql &> /dev/null; then
    echo "使用mysql客户端..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_NAME" < "$TEMP_SQL" 2>/dev/null || \
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" "$DB_NAME" < "$TEMP_SQL" 2>/dev/null || \
    mysql -u"$DB_USER" "$DB_NAME" < "$TEMP_SQL" 2>/dev/null || \
    echo "❌ MySQL连接失败，请手动执行SQL文件: $TEMP_SQL"
elif command -v mariadb &> /dev/null; then
    echo "使用mariadb客户端..."
    mariadb -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_NAME" < "$TEMP_SQL" 2>/dev/null || \
    mariadb -u"$DB_USER" "$DB_NAME" < "$TEMP_SQL" 2>/dev/null || \
    echo "❌ MariaDB连接失败，请手动执行SQL文件: $TEMP_SQL"
else
    echo "❌ 未找到MySQL/MariaDB客户端"
    echo "📋 请手动执行以下SQL文件: $TEMP_SQL"
    echo "或者复制以下内容到数据库管理工具中执行:"
    echo "----------------------------------------"
    cat "$TEMP_SQL"
    echo "----------------------------------------"
fi

# 清理临时文件
# rm -f "$TEMP_SQL"

echo "✅ 修复脚本执行完成"
echo "💡 如果数据库连接失败，请:"
echo "   1. 检查数据库服务是否运行"
echo "   2. 确认数据库连接信息"
echo "   3. 手动执行SQL文件: $TEMP_SQL"
