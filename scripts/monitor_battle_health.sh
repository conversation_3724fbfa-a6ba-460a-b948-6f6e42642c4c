#!/bin/bash
# 对战房间健康监控脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/var/log/battle_monitor.log"

# 创建日志文件
touch "$LOG_FILE"

# 记录日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查并修复对战房间
check_and_fix_rooms() {
    log_message "🔍 开始检查对战房间状态..."
    
    cd "$SCRIPT_DIR/.."
    python3 scripts/fix_battle_rooms.py --fix >> "$LOG_FILE" 2>&1
    
    if [ $? -eq 0 ]; then
        log_message "✅ 对战房间检查完成"
    else
        log_message "❌ 对战房间检查失败"
    fi
}

# 监控房间状态
monitor_rooms() {
    log_message "📊 监控房间状态..."
    
    cd "$SCRIPT_DIR/.."
    python3 scripts/fix_battle_rooms.py --monitor >> "$LOG_FILE" 2>&1
}

# 检查服务状态
check_services() {
    log_message "🔧 检查相关服务状态..."
    
    # 检查Redis连接
    if redis-cli ping > /dev/null 2>&1; then
        log_message "✅ Redis服务正常"
    else
        log_message "❌ Redis服务异常"
    fi
    
    # 检查数据库连接
    cd "$SCRIPT_DIR/.."
    python3 -c "
import os, sys, django
sys.path.insert(0, 'server')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()
from django.db import connection
try:
    connection.ensure_connection()
    print('✅ 数据库连接正常')
except Exception as e:
    print(f'❌ 数据库连接异常: {e}')
" >> "$LOG_FILE" 2>&1
}

# 清理旧日志
cleanup_logs() {
    # 保留最近7天的日志
    find /var/log -name "battle_monitor.log*" -mtime +7 -delete 2>/dev/null
    
    # 如果日志文件过大，进行轮转
    if [ -f "$LOG_FILE" ] && [ $(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null) -gt 10485760 ]; then
        mv "$LOG_FILE" "${LOG_FILE}.$(date +%Y%m%d_%H%M%S)"
        touch "$LOG_FILE"
        log_message "📝 日志文件已轮转"
    fi
}

# 主函数
main() {
    case "${1:-check}" in
        "check")
            check_and_fix_rooms
            ;;
        "monitor")
            monitor_rooms
            ;;
        "services")
            check_services
            ;;
        "full")
            cleanup_logs
            check_services
            monitor_rooms
            check_and_fix_rooms
            ;;
        "cleanup")
            cleanup_logs
            ;;
        *)
            echo "用法: $0 {check|monitor|services|full|cleanup}"
            echo "  check    - 检查并修复对战房间"
            echo "  monitor  - 监控房间状态"
            echo "  services - 检查服务状态"
            echo "  full     - 执行完整检查"
            echo "  cleanup  - 清理旧日志"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
