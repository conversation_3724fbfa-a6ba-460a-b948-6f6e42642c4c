#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复对战房间状态脚本
解决满员房间被错误取消的问题
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'server'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
django.setup()

from django.utils import timezone
from box.models import CaseRoom, CaseRoomBet, GameState, RoomType
from box.business_room import ready_to_run_room
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_and_fix_battle_rooms():
    """检查并修复对战房间状态"""
    
    print("🔍 开始检查对战房间状态...")
    
    # 1. 检查满员但长时间未开始的房间
    threshold_time = timezone.now() - timedelta(minutes=5)
    stuck_full_rooms = CaseRoom.objects.filter(
        state=GameState.Full.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=threshold_time
    )
    
    print(f"📊 发现 {stuck_full_rooms.count()} 个长时间卡在满员状态的房间")
    
    fixed_rooms = 0
    for room in stuck_full_rooms:
        try:
            bets_count = room.bets.count()
            print(f"🔧 检查房间 {room.short_id}: {bets_count}/{room.max_joiner} 参与者")
            
            if bets_count == room.max_joiner:
                # 满员房间，尝试推进
                print(f"  ✅ 房间确实满员，尝试推进...")
                ready_to_run_room(room.uid)
                fixed_rooms += 1
                print(f"  ✅ 房间 {room.short_id} 已推进")
            elif bets_count > 0:
                # 有参与者但未满员，重置为可加入状态
                room.state = GameState.Joinable.value
                room.save()
                print(f"  🔄 房间 {room.short_id} 重置为可加入状态")
                fixed_rooms += 1
            else:
                # 无参与者，取消房间
                room.state = GameState.Cancelled.value
                room.save()
                print(f"  ❌ 房间 {room.short_id} 无参与者，已取消")
                fixed_rooms += 1
                
        except Exception as e:
            print(f"  ❌ 修复房间 {room.short_id} 失败: {e}")
    
    # 2. 检查长时间运行的房间
    long_running_threshold = timezone.now() - timedelta(hours=1)
    long_running_rooms = CaseRoom.objects.filter(
        state=GameState.Running.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=long_running_threshold
    )
    
    print(f"📊 发现 {long_running_rooms.count()} 个长时间运行的房间")
    
    for room in long_running_rooms:
        try:
            print(f"🔧 检查长时间运行房间 {room.short_id}")
            # 尝试推进房间
            ready_to_run_room(room.uid)
            print(f"  ✅ 房间 {room.short_id} 已重新推进")
            fixed_rooms += 1
        except Exception as e:
            print(f"  ❌ 推进房间 {room.short_id} 失败: {e}")
    
    # 3. 统计信息
    active_rooms = CaseRoom.objects.filter(
        state__in=[GameState.Joinable.value, GameState.Full.value, GameState.Running.value],
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
    ).count()
    
    print(f"\n📈 修复完成:")
    print(f"  - 修复房间数: {fixed_rooms}")
    print(f"  - 当前活跃房间数: {active_rooms}")
    print(f"  - 检查时间: {timezone.now()}")

def monitor_room_states():
    """监控房间状态"""
    
    print("📊 房间状态统计:")
    
    states = [
        (GameState.Initial.value, "初始"),
        (GameState.Joinable.value, "可加入"),
        (GameState.Joining.value, "加入中"),
        (GameState.Full.value, "满员"),
        (GameState.Running.value, "进行中"),
        (GameState.End.value, "已结束"),
        (GameState.Cancelled.value, "已取消")
    ]
    
    for state_value, state_name in states:
        count = CaseRoom.objects.filter(
            state=state_value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        ).count()
        if count > 0:
            print(f"  {state_name}: {count} 个房间")
    
    # 检查最近的房间
    recent_rooms = CaseRoom.objects.filter(
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        create_time__gte=timezone.now() - timedelta(hours=1)
    ).order_by('-create_time')[:10]
    
    print(f"\n🕐 最近1小时创建的房间 (前10个):")
    for room in recent_rooms:
        state_name = dict(states).get(room.state, f"未知({room.state})")
        bets_count = room.bets.count()
        print(f"  {room.short_id}: {state_name} ({bets_count}/{room.max_joiner}) - {room.create_time}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="修复对战房间状态")
    parser.add_argument("--fix", action="store_true", help="修复问题房间")
    parser.add_argument("--monitor", action="store_true", help="监控房间状态")
    parser.add_argument("--all", action="store_true", help="执行所有操作")
    
    args = parser.parse_args()
    
    if args.all or args.monitor:
        monitor_room_states()
        print()
    
    if args.all or args.fix:
        check_and_fix_battle_rooms()
    
    if not any([args.fix, args.monitor, args.all]):
        print("请指定操作: --fix, --monitor, 或 --all")
        parser.print_help()
