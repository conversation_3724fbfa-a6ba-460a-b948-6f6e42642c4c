#!/usr/bin/env python3
# 简单修复脚本

import os
import sys

# 添加server路径
server_path = os.path.join(os.path.dirname(__file__), '..', 'server')
sys.path.insert(0, server_path)

# 设置Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

import django
django.setup()

from box.models import CaseRoom, GameState, RoomType
from django.utils import timezone
from datetime import timedelta

print("开始修复...")

# 查找卡住的满员房间
stuck_rooms = CaseRoom.objects.filter(
    state=GameState.Full.value,
    type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
    update_time__lt=timezone.now() - timedelta(minutes=3)
)

print(f"发现 {stuck_rooms.count()} 个卡住的房间")

for room in stuck_rooms:
    bets_count = room.bets.count()
    print(f"修复房间 {room.short_id}: {bets_count}/{room.max_joiner}")
    
    if bets_count == room.max_joiner:
        room.state = GameState.Running.value
        room.save()
        print(f"  设置为运行状态")
    elif bets_count > 0:
        room.state = GameState.Joinable.value
        room.save()
        print(f"  重置为可加入状态")

print("修复完成")
