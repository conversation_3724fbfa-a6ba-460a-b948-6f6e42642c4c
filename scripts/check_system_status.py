#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统状态检查脚本
检查对战系统的各个组件状态
"""

import os
import sys
import subprocess
import socket
import time
import json
from datetime import datetime

def check_port(host, port, timeout=3):
    """检查端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False

def check_process(pattern):
    """检查进程是否运行"""
    try:
        result = subprocess.run(
            ['pgrep', '-f', pattern],
            capture_output=True,
            text=True
        )
        return len(result.stdout.strip()) > 0
    except Exception:
        return False

def check_docker_container(pattern):
    """检查Docker容器状态"""
    try:
        result = subprocess.run(
            ['docker', 'ps', '--format', '{{.Names}}'],
            capture_output=True,
            text=True
        )
        containers = result.stdout.strip().split('\n')
        return any(pattern in container for container in containers if container)
    except Exception:
        return False

def check_django_health():
    """检查Django应用健康状态"""
    try:
        # 尝试连接Django端口
        if check_port('localhost', 8000):
            return "🟢 运行中"
        elif check_port('localhost', 8001):
            return "🟢 运行中 (8001端口)"
        else:
            return "🔴 未运行"
    except Exception:
        return "❓ 检查失败"

def check_database():
    """检查数据库连接"""
    try:
        # 添加Django路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'server'))
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
        
        import django
        django.setup()
        
        from django.db import connection
        connection.ensure_connection()
        return "🟢 连接正常"
    except Exception as e:
        return f"🔴 连接失败: {str(e)[:50]}"

def check_redis():
    """检查Redis连接"""
    try:
        result = subprocess.run(
            ['redis-cli', 'ping'],
            capture_output=True,
            text=True,
            timeout=5
        )
        if 'PONG' in result.stdout:
            return "🟢 连接正常"
        else:
            return "🔴 连接失败"
    except Exception:
        return "❓ Redis未安装或不可用"

def check_async_system():
    """检查异步系统状态"""
    try:
        # 添加Django路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'server'))
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
        
        import django
        django.setup()
        
        from box.enhanced_async_system import get_battle_async_processor
        processor = get_battle_async_processor()
        stats = processor.get_stats()
        
        if stats['is_running']:
            return f"🟢 运行中 (活跃任务: {stats['active_tasks']}, 队列: {stats['queue_size']})"
        else:
            return "🔴 未运行"
    except Exception as e:
        return f"❓ 检查失败: {str(e)[:50]}"

def check_battle_rooms():
    """检查对战房间状态"""
    try:
        # 添加Django路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'server'))
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
        
        import django
        django.setup()
        
        from box.models import CaseRoom, GameState, RoomType
        from django.utils import timezone
        from datetime import timedelta
        
        # 统计各状态房间数量
        states = {
            GameState.Joinable.value: "可加入",
            GameState.Full.value: "满员",
            GameState.Running.value: "进行中",
            GameState.End.value: "已结束",
            GameState.Cancelled.value: "已取消"
        }
        
        room_stats = {}
        total_rooms = 0
        
        for state_value, state_name in states.items():
            count = CaseRoom.objects.filter(
                state=state_value,
                type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
            ).count()
            if count > 0:
                room_stats[state_name] = count
                total_rooms += count
        
        # 检查卡住的房间
        threshold_time = timezone.now() - timedelta(minutes=5)
        stuck_rooms = CaseRoom.objects.filter(
            state=GameState.Full.value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=threshold_time
        ).count()
        
        status = f"总计: {total_rooms} 个房间"
        if room_stats:
            status += f" ({', '.join(f'{k}:{v}' for k, v in room_stats.items())})"
        
        if stuck_rooms > 0:
            status += f" ⚠️ {stuck_rooms}个卡住"
            return f"🟡 {status}"
        else:
            return f"🟢 {status}"
            
    except Exception as e:
        return f"❓ 检查失败: {str(e)[:50]}"

def main():
    print("🔍 系统状态检查")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查各个组件
    checks = [
        ("Django应用", check_django_health),
        ("数据库连接", check_database),
        ("Redis连接", check_redis),
        ("增强异步系统", check_async_system),
        ("对战房间状态", check_battle_rooms),
    ]
    
    # 检查进程
    processes = [
        ("Python服务", "python.*manage.py|python.*gunicorn|python.*uwsgi"),
        ("Celery Worker", "celery.*worker"),
        ("thworker", "thworker"),
    ]
    
    print("📋 组件状态:")
    for name, check_func in checks:
        try:
            status = check_func()
            print(f"  {name}: {status}")
        except Exception as e:
            print(f"  {name}: ❌ 检查异常: {e}")
    
    print("\n📋 进程状态:")
    for name, pattern in processes:
        if check_process(pattern):
            print(f"  {name}: 🟢 运行中")
        else:
            print(f"  {name}: 🔴 未运行")
    
    print("\n📋 端口状态:")
    ports = [
        ("Django (8000)", 8000),
        ("Django (8001)", 8001),
        ("Redis (6379)", 6379),
        ("MySQL (3306)", 3306),
        ("PostgreSQL (5432)", 5432),
    ]
    
    for name, port in ports:
        if check_port('localhost', port):
            print(f"  {name}: 🟢 开放")
        else:
            print(f"  {name}: 🔴 关闭")
    
    # 检查Docker容器
    print("\n📋 Docker容器:")
    docker_patterns = ["django", "celery", "worker", "web", "db", "redis"]
    
    for pattern in docker_patterns:
        if check_docker_container(pattern):
            print(f"  {pattern}: 🟢 运行中")
    
    print("\n💡 建议操作:")
    print("  - 如果发现问题，运行: bash scripts/simple_restart.sh")
    print("  - 查看详细日志: python3 scripts/monitor_logs.py --analyze")
    print("  - 修复房间状态: python3 server/manage.py fix_battle_rooms")

if __name__ == "__main__":
    main()
