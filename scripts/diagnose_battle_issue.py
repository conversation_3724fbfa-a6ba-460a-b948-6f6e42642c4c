#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断对战房间问题
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'server'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

from django.utils import timezone
from box.models import CaseRoom, CaseRoomBet, GameState, RoomType

def diagnose_rooms():
    """诊断房间状态"""
    
    print("🔍 诊断对战房间状态...")
    print("=" * 50)
    
    # 1. 统计各状态房间数量
    print("📊 房间状态统计:")
    states = [
        (GameState.Initial.value, "初始"),
        (GameState.Joinable.value, "可加入"),
        (GameState.Joining.value, "加入中"),
        (GameState.Full.value, "满员"),
        (GameState.Running.value, "进行中"),
        (GameState.End.value, "已结束"),
        (GameState.Cancelled.value, "已取消")
    ]
    
    total_rooms = 0
    for state_value, state_name in states:
        count = CaseRoom.objects.filter(
            state=state_value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        ).count()
        if count > 0:
            print(f"  {state_name}: {count} 个")
            total_rooms += count
    
    print(f"  总计: {total_rooms} 个对战房间")
    print()
    
    # 2. 检查满员房间详情
    full_rooms = CaseRoom.objects.filter(
        state=GameState.Full.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
    ).order_by('-update_time')
    
    print(f"🔍 满员房间详情 ({full_rooms.count()} 个):")
    if full_rooms.exists():
        for room in full_rooms[:10]:  # 只显示前10个
            bets_count = room.bets.count()
            time_diff = timezone.now() - room.update_time
            print(f"  房间 {room.short_id}: {bets_count}/{room.max_joiner} 参与者, "
                  f"更新时间: {time_diff.total_seconds():.0f}秒前")
    else:
        print("  无满员房间")
    print()
    
    # 3. 检查最近取消的房间
    recent_cancelled = CaseRoom.objects.filter(
        state=GameState.Cancelled.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__gte=timezone.now() - timedelta(hours=1)
    ).order_by('-update_time')
    
    print(f"⚠️ 最近1小时取消的房间 ({recent_cancelled.count()} 个):")
    if recent_cancelled.exists():
        for room in recent_cancelled[:5]:  # 只显示前5个
            bets_count = room.bets.count()
            time_diff = timezone.now() - room.update_time
            print(f"  房间 {room.short_id}: {bets_count}/{room.max_joiner} 参与者, "
                  f"取消时间: {time_diff.total_seconds():.0f}秒前")
    else:
        print("  无最近取消的房间")
    print()
    
    # 4. 检查长时间卡住的房间
    threshold_time = timezone.now() - timedelta(minutes=5)
    stuck_rooms = CaseRoom.objects.filter(
        state__in=[GameState.Full.value, GameState.Running.value],
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=threshold_time
    )
    
    print(f"🚨 长时间卡住的房间 ({stuck_rooms.count()} 个):")
    if stuck_rooms.exists():
        for room in stuck_rooms:
            bets_count = room.bets.count()
            time_diff = timezone.now() - room.update_time
            state_name = dict(states).get(room.state, f"未知({room.state})")
            print(f"  房间 {room.short_id}: {state_name}, {bets_count}/{room.max_joiner} 参与者, "
                  f"卡住时间: {time_diff.total_seconds():.0f}秒")
    else:
        print("  无卡住的房间")
    print()
    
    # 5. 检查最近创建的房间
    recent_rooms = CaseRoom.objects.filter(
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        create_time__gte=timezone.now() - timedelta(minutes=30)
    ).order_by('-create_time')
    
    print(f"🆕 最近30分钟创建的房间 ({recent_rooms.count()} 个):")
    if recent_rooms.exists():
        for room in recent_rooms[:5]:  # 只显示前5个
            bets_count = room.bets.count()
            state_name = dict(states).get(room.state, f"未知({room.state})")
            time_diff = timezone.now() - room.create_time
            print(f"  房间 {room.short_id}: {state_name}, {bets_count}/{room.max_joiner} 参与者, "
                  f"创建时间: {time_diff.total_seconds():.0f}秒前")
    else:
        print("  无最近创建的房间")

def quick_fix_stuck_rooms():
    """快速修复卡住的房间"""
    
    print("\n🔧 开始修复卡住的房间...")
    
    threshold_time = timezone.now() - timedelta(minutes=5)
    stuck_rooms = CaseRoom.objects.filter(
        state=GameState.Full.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=threshold_time
    )
    
    fixed_count = 0
    for room in stuck_rooms:
        try:
            bets_count = room.bets.count()
            print(f"🔧 修复房间 {room.short_id}: {bets_count}/{room.max_joiner}")
            
            if bets_count == room.max_joiner:
                # 满员房间，设置为运行状态
                room.state = GameState.Running.value
                room.save()
                print(f"  ✅ 设置为运行状态")
                fixed_count += 1
            elif bets_count > 0:
                # 有参与者但未满员
                room.state = GameState.Joinable.value
                room.save()
                print(f"  🔄 重置为可加入状态")
                fixed_count += 1
            else:
                # 无参与者
                room.state = GameState.Cancelled.value
                room.save()
                print(f"  ❌ 取消房间（无参与者）")
                fixed_count += 1
                
        except Exception as e:
            print(f"  ❌ 修复失败: {e}")
    
    print(f"\n✅ 修复完成，共处理 {fixed_count} 个房间")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="诊断对战房间问题")
    parser.add_argument("--fix", action="store_true", help="修复卡住的房间")
    
    args = parser.parse_args()
    
    diagnose_rooms()
    
    if args.fix:
        quick_fix_stuck_rooms()
