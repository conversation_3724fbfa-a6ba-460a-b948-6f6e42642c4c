#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复对战房间问题
"""

import os
import sys

# 添加server目录到Python路径
server_path = os.path.join(os.path.dirname(__file__), '..', 'server')
sys.path.insert(0, server_path)

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')

try:
    import django
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

from django.utils import timezone
from datetime import timedelta

try:
    from box.models import CaseRoom, GameState, RoomType
    print("✅ 模型导入成功")
except Exception as e:
    print(f"❌ 模型导入失败: {e}")
    sys.exit(1)

def quick_fix():
    """快速修复满员房间问题"""
    
    print("🔍 检查满员房间状态...")
    
    # 查找长时间卡在满员状态的房间
    threshold_time = timezone.now() - timedelta(minutes=5)
    stuck_rooms = CaseRoom.objects.filter(
        state=GameState.Full.value,
        type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
        update_time__lt=threshold_time
    )
    
    print(f"📊 发现 {stuck_rooms.count()} 个卡住的满员房间")
    
    fixed_count = 0
    for room in stuck_rooms:
        try:
            bets_count = room.bets.count()
            print(f"🔧 处理房间 {room.short_id}: {bets_count}/{room.max_joiner}")
            
            if bets_count == room.max_joiner:
                # 满员房间，重置为运行状态
                room.state = GameState.Running.value
                room.save()
                print(f"  ✅ 房间 {room.short_id} 已设置为运行状态")
                fixed_count += 1
            elif bets_count > 0:
                # 有参与者但未满员
                room.state = GameState.Joinable.value
                room.save()
                print(f"  🔄 房间 {room.short_id} 重置为可加入状态")
                fixed_count += 1
            else:
                # 无参与者
                room.state = GameState.Cancelled.value
                room.save()
                print(f"  ❌ 房间 {room.short_id} 已取消（无参与者）")
                fixed_count += 1
                
        except Exception as e:
            print(f"  ❌ 处理房间 {room.short_id} 失败: {e}")
    
    print(f"\n✅ 修复完成，共处理 {fixed_count} 个房间")
    
    # 显示当前状态
    print("\n📊 当前房间状态统计:")
    states = [
        (GameState.Joinable.value, "可加入"),
        (GameState.Full.value, "满员"),
        (GameState.Running.value, "进行中"),
        (GameState.End.value, "已结束"),
        (GameState.Cancelled.value, "已取消")
    ]
    
    for state_value, state_name in states:
        count = CaseRoom.objects.filter(
            state=state_value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        ).count()
        if count > 0:
            print(f"  {state_name}: {count} 个")

if __name__ == "__main__":
    quick_fix()
