# HomeStats监控数据实时更新 - Docker部署指南

## 📋 更新概述

本次更新解决了HomeStats组件中统计数据不实时更新的问题，确保用户总数、开箱总数、对战总数等数据能够在实际业务操作时实时更新并推送到前端。

## 🔧 主要修改

### 1. 后端修改

#### 监控线程自动启动
- **文件**: `server/monitor/apps.py`
- **修改**: 在Django应用启动时自动启动监控数据更新线程
- **作用**: 确保监控线程在容器启动时自动运行

#### 用户注册时的实时推送
- **文件**: `server/authentication/model_signals.py`
- **修改**: 用户注册时同时推送到 `total_users` 和 `monitor` 频道
- **作用**: 用户注册时实时更新用户总数

#### 开箱时的实时推送
- **文件**: `server/box/business.py`
- **修改**: 开箱完成后推送最新统计数据
- **作用**: 开箱时实时更新开箱总数

#### 对战创建时的实时推送
- **文件**: `server/box/business_room.py`
- **修改**: 对战房间创建后推送最新统计数据
- **作用**: 创建对战时实时更新对战总数

#### 统计数据源优化
- **文件**: `server/monitor/business.py`
- **修改**: 使用实际数据库统计 + 配置基数的混合方式
- **作用**: 确保数据的准确性和实时性

### 2. Docker配置修改

#### 部署脚本修复
- **文件**: `docker-deploy.sh`
- **修改**: 修复thworker服务名称错误
- **作用**: 确保监控线程容器正确启动

## 🚀 部署步骤

### 方式一：使用更新脚本（推荐）

```bash
# 完整更新部署
./update_docker_monitor.sh update

# 测试监控数据
./update_docker_monitor.sh test

# 查看监控日志
./update_docker_monitor.sh logs

# 验证监控功能
./update_docker_monitor.sh verify
```

### 方式二：手动部署

```bash
# 1. 停止现有容器
docker-compose down

# 2. 重新构建镜像
docker-compose build --no-cache web thworker-compat

# 3. 启动服务
docker-compose up -d redis
sleep 5
docker-compose up -d web
sleep 10
docker-compose up -d celery-worker celery-beat thworker-compat
sleep 5
docker-compose up -d websocket

# 4. 验证服务
docker-compose ps
curl http://localhost:8000/api/health/
curl http://localhost:4000/health
```

## 📊 验证监控功能

### 1. 检查服务状态
```bash
docker-compose ps
```

### 2. 测试监控API
```bash
# 获取当前统计数据
curl -s http://localhost:8000/api/monitor/data/ | grep -o '"stats":{[^}]*}'
```

### 3. 查看监控日志
```bash
# Django服务日志
docker-compose logs web | grep -i monitor

# ThWorker服务日志
docker-compose logs thworker-compat

# WebSocket服务日志
docker-compose logs websocket
```

### 4. 验证实时更新
- 注册新用户 → 用户总数应该实时增加
- 进行开箱操作 → 开箱总数应该实时增加
- 创建对战房间 → 对战总数应该实时增加
- 在线人数每8-12秒动态变化

## 🔍 故障排除

### 监控线程未启动
```bash
# 检查Django应用日志
docker-compose logs web | grep "Monitor"

# 应该看到：
# INFO:monitor.apps:[Monitor] 启动监控数据更新线程...
# INFO:monitor.apps:[Monitor] 监控数据更新线程已启动
```

### 统计数据不更新
```bash
# 检查ThWorker服务状态
docker-compose logs thworker-compat

# 检查Redis连接
docker exec csgoskins-redis redis-cli ping
```

### WebSocket连接问题
```bash
# 检查WebSocket服务健康状态
curl http://localhost:4000/health

# 检查WebSocket日志
docker-compose logs websocket
```

## 📈 监控数据流程

```mermaid
graph TD
    A[用户注册] --> B[推送用户总数更新]
    C[用户开箱] --> D[推送开箱总数更新]
    E[创建对战] --> F[推送对战总数更新]
    G[监控线程] --> H[推送在线人数更新]
    
    B --> I[Redis Pub/Sub]
    D --> I
    F --> I
    H --> I
    
    I --> J[WebSocket服务]
    J --> K[前端HomeStats组件]
    K --> L[实时显示更新]
```

## ✅ 预期效果

部署完成后，HomeStats组件应该能看到：

1. **用户总数** - 每当有新用户注册时实时增加
2. **开箱总数** - 每当有用户开箱时实时增加
3. **对战总数** - 每当创建新对战时实时增加
4. **在线人数** - 每8-12秒动态变化

## 📝 注意事项

1. **数据持久性**: 统计数据基于实际数据库记录，重启容器不会丢失
2. **性能影响**: 实时推送对系统性能影响很小
3. **兼容性**: 保持与现有API的完全兼容
4. **监控**: 建议定期检查监控线程运行状态

## 🔄 回滚方案

如果需要回滚到之前版本：

```bash
# 1. 停止服务
docker-compose down

# 2. 恢复代码到之前版本
git checkout <previous-commit>

# 3. 重新构建和启动
docker-compose build --no-cache
docker-compose up -d
```
