# 项目文件组织结构

## 📁 目录规范

### **根目录结构**
```
csgoskins.com.cn/
├── server/                 # 后端Django项目
├── ui/                     # 前端Nuxt项目  
├── docs/                   # 文档目录
├── scripts/                # 脚本目录
├── logs/                   # 日志目录
├── backup/                 # 备份目录
└── deployment/             # 部署配置
```

### **文档目录 (docs/)**
```
docs/
├── api/                    # API文档
├── backend/                # 后端文档
├── frontend/               # 前端文档
├── deployment/             # 部署文档
├── guides/                 # 使用指南
├── fixes/                  # 修复记录
│   ├── BATTLE_ROOM_FIXES_FINAL.md
│   ├── BATTLE_FIXES_COMPLETED.md
│   └── ...
└── temp/                   # 临时文档
```

### **脚本目录 (scripts/)**
```
scripts/
├── battle_fixes/           # 对战修复脚本
│   ├── fix_running_rooms.py
│   ├── fix_stuck_battles_now.py
│   ├── check_stuck_status.py
│   └── ...
├── maintenance/            # 维护脚本
├── monitoring/             # 监控脚本
├── migrations/             # 迁移脚本
├── tools/                  # 工具脚本
└── tests/                  # 测试脚本
```

### **服务器目录 (server/)**
```
server/
├── box/                    # 开箱应用
│   ├── management/
│   │   └── commands/
│   │       └── cleanup_stuck_battles.py
│   ├── models.py
│   ├── business_room.py
│   └── ...
├── authentication/         # 认证应用
├── articles/              # 文章应用
├── logs/                  # 应用日志
├── static/                # 静态文件
├── media/                 # 媒体文件
└── manage.py
```

## 🔧 文件分类规则

### **文档文件 (.md)**
- **修复记录** → `docs/fixes/`
- **API文档** → `docs/api/`
- **部署文档** → `docs/deployment/`
- **使用指南** → `docs/guides/`
- **临时文档** → `docs/temp/`

### **脚本文件 (.py, .sh, .sql)**
- **对战相关** → `scripts/battle_fixes/`
- **维护脚本** → `scripts/maintenance/`
- **监控脚本** → `scripts/monitoring/`
- **工具脚本** → `scripts/tools/`
- **测试脚本** → `scripts/tests/`

### **配置文件**
- **Docker配置** → `deployment/`
- **Nginx配置** → `deployment/`
- **环境配置** → 根目录或`deployment/`

### **日志文件**
- **应用日志** → `logs/`
- **服务日志** → `logs/`
- **错误日志** → `logs/`

### **备份文件**
- **代码备份** → `backup/`
- **数据备份** → `backup/`
- **配置备份** → `backup/`

## 📋 文件命名规范

### **文档命名**
- 使用大写字母和下划线：`BATTLE_FIXES_FINAL.md`
- 包含日期的文档：`DEPLOYMENT_GUIDE_20250722.md`
- 临时文档加`TEMP_`前缀：`TEMP_NOTES.md`

### **脚本命名**
- 使用小写字母和下划线：`fix_battle_rooms.py`
- 功能明确的命名：`cleanup_stuck_battles.py`
- 临时脚本加`temp_`前缀：`temp_test.py`

### **日志命名**
- 按服务分类：`django.log`, `websocket.log`
- 包含日期：`battle_fix_20250722.log`
- 错误日志：`error.log`, `debug.log`

## 🗂️ 已整理的文件

### **移动到 docs/fixes/**
- `BATTLE_ROOM_FIXES_FINAL.md`
- `BATTLE_FIXES_COMPLETED.md`
- `BATTLE_FIXES_SUMMARY.md`
- `BATTLE_ENDING_FIX.md`
- `BATTLE_PROGRESSION_FIX.md`
- `BATTLE_SYSTEM_UPGRADE.md`

### **移动到 scripts/battle_fixes/**
- `fix_running_rooms.py`
- `fix_stuck_battles_now.py`
- `fix_rds_battles.sql`
- `fix_rds_python.py`
- `check_stuck_status.py`
- `simple_check.py`
- `test_battle_fixes.py`
- `quick_test.py`

### **移动到 docs/temp/**
- `emergency_fix.sh`
- `system_diagnosis.sh`

## 🎯 维护建议

1. **定期清理** - 每月清理临时文件和过期日志
2. **文档更新** - 重要修改后及时更新相关文档
3. **备份管理** - 定期清理旧备份，保留重要版本
4. **脚本整理** - 将一次性脚本移到temp目录
5. **日志轮转** - 配置日志轮转防止磁盘空间不足

## 📝 注意事项

- 所有临时文件应放在对应的`temp/`目录下
- 重要的修复记录应保存在`docs/fixes/`中
- 生产环境脚本应经过测试后放在对应功能目录
- 文档应使用中文编写，便于团队理解
- 定期检查和更新目录结构文档
