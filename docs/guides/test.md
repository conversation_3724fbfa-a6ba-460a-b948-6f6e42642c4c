## 目录规范：
- 后端目录:server
- 前端目录:ui
- 文档目录:docs
- 脚本目录:scripts
- 配置目录:deployment
- 备份目录:backup
- 日志目录:logs

## 创建文档规范
- 减少创建修复文档，只创建必要的文档

## 系统环境
- 当前开发环境是服务器环境，ip：************
- 数据库是独立的阿里云RDS
- 开发环境：django端口9000，ui端口3000，socket端口映射到了域名：https://socket.cs2.net.cn
- 生成环境docker容器：服务端已经映射了域名：https://api.cs2.net.cn，socket映射了域名：https://socket.cs2.net.cn
- 所有后端服务都是docker容器，如果要验证服务是否正常，直接查看容器日志，修改完代码后，要重启容器才能生效
- python版本：3.8
- django版本：3.2
- server目录下使用虚拟环境：source venv/bin/activate

## 代码提交规范
- 代码提交前，先运行前端和后端的测试用例，确保没有问题
- 前端测试页面存放在ui/page/demo，并加入演示导航ui/page/demo/index.vue
- 前端代码完成后运行npm run build，确保没有问题
- 后端代码完成后运行python manage.py test，确保没有问题

## bug修复规范
- 前后端结合分析问题，找到最佳修复方案
- server端修改后要重启容器让代码生效
- 修复后要进行回归测试，确保没有引入新问题