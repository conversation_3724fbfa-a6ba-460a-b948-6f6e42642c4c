---
mode: ask
---
# Copilot Prompt Examples

本文件用于提供给 GitHub Copilot 一些示例提示，以便它更准确地补全符合项目风格和功能预期的代码。

## 函数实现类提示

```ts
// 在 utils/date.ts 中实现将时间戳格式化为 yyyy-MM-dd 的函数
```

```js
// 实现一个深拷贝函数，能处理对象、数组和基本类型
```

```py
# 在 services/user_service.py 中编写一个注册用户的逻辑，包含数据校验和密码加密
```

```ts
// 创建一个响应式 Vue 组件，用于展示用户个人信息卡片
```

## 接口请求类提示

```ts
// 在 api/user.ts 中封装获取用户详情的 GET 请求
```

```js
// 使用 fetch 封装通用的 GET/POST 请求方法
```

```py
# 使用 requests 库编写一个获取外部 API 数据的工具函数
```

## 数据模型类提示

```ts
// 创建一个 TypeScript 接口类型定义用户结构，包含 id、name、email 和 roles
```

```py
# 使用 Django ORM 定义一个 BlogPost 模型，包含标题、内容、作者、发布时间
```

## UI组件类提示

```vue
<!-- 创建一个弹窗组件，支持传入标题和插槽内容 -->
```

```ts
// 创建一个使用 Tailwind CSS 样式的按钮组件，支持 loading 状态
```

## 测试类提示

```js
// 使用 Jest 编写 formatDate 函数的单元测试
```

```py
# 使用 pytest 测试注册用户时的输入校验逻辑
```

## 业务逻辑类提示

```ts
// 判断用户是否拥有访问权限：管理员或拥有指定权限码
```

```py
# 处理订单支付成功后的业务流程：更新订单状态、发放积分、发送通知
```

## 国际化类提示

```ts
// 使用 vue-i18n 实现页面标题的多语言切换
```

## CSGO开箱游戏特定提示

```ts
// 在 composables/useCaseOpening.ts 中实现开箱动画逻辑，包含滚动动画和结果展示
```

```vue
<!-- 创建一个开箱动画组件，支持物品滚动、减速停止和结果展示 -->
```

```ts
// 在 services/case-api.ts 中封装开箱API请求，包含错误处理和重试机制
```

```py
# 在 box/business.py 中实现开箱逻辑，包含概率计算、物品发放和记录保存
```

```ts
// 在 composables/useBattleState.ts 中管理对战状态，包含玩家信息、轮次管理和结果统计
```

```vue
<!-- 创建一个对战房间组件，支持多玩家同时开箱和实时结果展示 -->
```

```ts
// 在 utils/websocket.ts 中实现WebSocket连接管理，支持断线重连和消息队列
```

```py
# 在 websocket/consumers.py 中实现WebSocket消费者，处理实时游戏状态推送
```

```ts
// 在 stores/user.ts 中实现用户状态管理，包含登录状态、余额和开箱历史
```

```py
# 在 authentication/models.py 中定义用户模型，包含Steam绑定、交易链接等字段
```

```ts
// 在 utils/audio.ts 中实现音效管理，支持开箱音效、背景音乐和音量控制
```

```vue
<!-- 创建一个物品展示组件，支持稀有度颜色、价格显示和操作按钮 -->
```

```ts
// 在 composables/useAnimationSync.ts 中实现动画同步，确保多玩家动画协调一致
```

```py
# 在 box/models.py 中定义箱子模型，包含价格、掉落物品和概率配置
```

## 其他提示建议

- 使用英文注释增强 Copilot 补全精度；
- 将 prompt 置于函数顶部或注释中能引导更合理的建议；
- 尽量提供类型、结构和上下文变量名；
- 多使用"// 实现...功能"、"# 编写...逻辑"风格作为起始词；
- 对于游戏相关功能，优先考虑用户体验和动画流畅性；
- 对于支付和交易功能，重点关注安全性和数据一致性；
- 对于实时功能，考虑WebSocket连接稳定性和消息可靠性。
