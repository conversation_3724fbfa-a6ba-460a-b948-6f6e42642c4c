---
applyTo: '**'
---
# Copilot Instructions

本文件用于指导 GitHub Copilot 更好地为本项目生成代码建议。

## 项目简介（Project Overview）

- 项目名称：CSGO Skins 开箱游戏平台
- 项目用途与目标：提供CSGO皮肤开箱、对战、交易等功能的在线游戏平台
- 使用技术栈：
  - 前端：Nuxt.js 3 + Vue 3 + TypeScript + Tailwind CSS + Pinia
  - 后端：Django 3.2 + Python + MySQL + Redis + Celery
  - 实时通信：WebSocket (Socket.IO)
  - 动画：GSAP + Three.js
  - 音效：Web Audio API
  - 国际化：vue-i18n

## 文件结构说明（Project Structure）

```
/ui                    # 前端代码 (Nuxt.js)
  /components          # Vue组件
    /battle            # 对战相关组件
    /case              # 开箱相关组件
    /auth              # 认证相关组件
  /composables         # Vue组合式函数
  /pages               # 页面组件
  /stores              # Pinia状态管理
  /services            # API服务封装
  /utils               # 工具函数
  /types               # TypeScript类型定义
  /locales             # 国际化文件

/server                # 后端代码 (Django)
  /box                 # 开箱游戏模块
  /battle              # 对战模块
  /authentication      # 用户认证模块
  /charge              # 充值模块
  /market              # 市场交易模块
  /websocket           # WebSocket通信
  /monitor             # 监控统计
  /utils               # 工具函数
```

## 编码规范（Code Style Guide）

- 命名规范：
  - 变量和函数：camelCase
  - 类名和组件名：PascalCase
  - 常量：UPPER_SNAKE_CASE
  - 文件名：kebab-case
- 缩进风格：2个空格
- 引号风格：单引号优先
- 语句结束：JavaScript/TypeScript使用分号
- 注释语言：中文注释，英文变量名

## 功能模块（Feature Modules）

- 用户模块：注册、登录、Steam绑定、个人资料管理
- 开箱模块：箱子选择、开箱动画、结果展示、历史记录
- 对战模块：房间创建、多人对战、实时同步、结果统计
- 交易模块：物品出售、购买、Steam交易、余额管理
- 充值模块：多种支付方式、订单管理、自动到账
- 市场模块：物品展示、价格查询、交易历史
- 实时通信：WebSocket连接、消息推送、状态同步

## 常用工具函数（Utilities）

- `utils/date.ts`：时间格式化、时区转换
- `utils/websocket.ts`：WebSocket连接管理
- `utils/audio.ts`：音效播放控制
- `utils/animation.ts`：动画工具函数
- `utils/validation.ts`：数据验证工具
- `utils/format.ts`：数据格式化工具

## 注释风格（Commenting Style）

使用中文注释，重要函数使用JSDoc格式：

```ts
/**
 * 执行开箱操作
 * @param caseId 箱子ID
 * @param count 开箱次数
 * @returns 开箱结果
 */
async function openCase(caseId: string, count: number = 1) {
  // 实现逻辑
}
```

## 自动补全建议策略（Copilot Suggestions Guidance）

- 优先推荐：
  - Vue 3 Composition API 模式
  - TypeScript 类型定义
  - Tailwind CSS 样式类
  - Pinia 状态管理
  - 响应式设计模式
- 避免推荐：
  - 硬编码的敏感信息
  - 过度复杂的嵌套结构
  - 不安全的用户输入处理
- 架构模式：组件化、模块化、响应式

## 安全性与性能（Security and Performance）

- 安全要求：
  - 所有用户输入必须验证和转义
  - 敏感操作需要权限验证
  - API请求需要CSRF保护
  - 支付相关操作需要签名验证
- 性能要求：
  - 图片懒加载和压缩
  - 动画使用CSS3和GSAP优化
  - API响应缓存
  - WebSocket连接复用

## 测试规范（Testing Guide）

- 测试框架：Vitest (前端) + pytest (后端)
- 覆盖范围：核心业务逻辑、API接口、组件交互
- 测试文件命名：`*.test.ts` 或 `*_test.py`
- 测试类型：单元测试、集成测试、E2E测试

## 示例补全提示（Example Prompts）

```ts
// 在 composables/useCaseOpening.ts 中实现开箱动画逻辑
```

```vue
<!-- 创建一个开箱结果展示组件，支持物品信息和操作按钮 -->
```

```py
# 在 box/business.py 中实现开箱概率计算和物品发放逻辑
```

```ts
// 在 services/case-api.ts 中封装开箱API请求
```

```vue
<!-- 创建一个对战房间组件，支持多玩家状态显示 -->
```

```py
# 在 websocket/consumers.py 中处理实时游戏状态推送
```

## 其他约定（Other Conventions）

- 环境变量命名：`NUXT_PUBLIC_*` (前端公共变量)
- 国际化文件：`locales/zh-hans.json` 和 `locales/en.json`
- 文件命名：组件使用PascalCase，工具函数使用camelCase
- API接口：RESTful风格，使用JSON格式
- 错误处理：统一的错误码和消息格式
- 日志记录：结构化日志，包含用户ID和操作类型
