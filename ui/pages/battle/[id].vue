<template>
  <div class="battle-detail-page px-4">
    <!-- 实时模式连接状态 -->
    <BattleConnectionStatus
      v-show="unifiedIsRealtimeMode"
      :is-connected="connectionState.isConnected"
      :is-reconnecting="connectionState.isReconnecting"
      :reconnect-attempts="connectionState.reconnectAttempts"
      @retry-connection="handleRetryConnection"
    />

    <!-- 加载状态：骨架屏 -->
    <BattleDetailSkeleton v-if="pageController.pageState.value.isLoading" />

    <!-- 错误状态 -->
    <div v-if="pageController.pageState.value.error" class="error-container">
      <div class="error-content">
        <Icon name="heroicons:exclamation-triangle" class="w-8 h-8 text-red-400" />
        <p class="text-red-400 mt-2">{{ pageController.pageState.value.error }}</p>
        <button 
          @click="pageController.handleBattleAction('refresh-data')"
          class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          {{ t('common.retry') }}
        </button>
      </div>
    </div>

    <!-- 静态模式对战内容（已初始化后显示） -->
    <div v-if="pageController.pageState.value.isInitialized && !unifiedIsRealtimeMode" class="battle-content">
      <div class="content-container">
        <BattleStaticModeIndicator
          :battle-state="unifiedBattleData.state"
          :finished-time="unifiedBattleData.update_time"
        />
        <!-- 对战头部信息 -->
        <BattleHeader
          :battle-data="unifiedBattleData"
          :is-user-creator="isUserCreator"
          :is-user-joined="isUserJoined"
          :is-battle-started="isBattleStarted"
        />
        <!-- 对战状态显示 -->
        <BattleStateDisplay
          :battle-state="battleStateString"
          :current-round="unifiedCurrentRound"
          :total-rounds="unifiedTotalRounds"
          :opening-case-id="openingCaseId"
          :is-battle-started="isBattleStarted"
          :is-battle-finished="isBattleFinished"
        />
        <!-- 箱子展示 -->
        <BattleCaseDisplay
          :cases="[...displayCases]"
          :current-round="unifiedCurrentRound"
          :total-rounds="unifiedTotalRounds"
          :opening-case-id="openingCaseId"
          :is-battle-started="isBattleStarted"
          :is-battle-finished="isBattleFinished"
        />
        <!-- 玩家展示 -->
        <BattlePlayerDisplay
          :players="unifiedBattleData.bets || []"
          :current-round="unifiedCurrentRound"
          :total-rounds="unifiedTotalRounds"
          :max-players="unifiedBattleData.max_joiner || unifiedBattleData.bets.length"
          :is-battle-started="isBattleStarted"
          :is-battle-finished="isBattleFinished"
          :host-uid="unifiedBattleData.user?.uid"
          :is-user-creator="isUserCreator"
          :is-user-joined="isUserJoined"
          :current-user-id="currentUserId"
          :opening-player-name="openingPlayerName"
          :opening-player-index="openingPlayerIndex"
          :current-case-items="[...currentCaseItems]"
          :steps="calculationSteps"
          :can-join="canJoinBattle"
          :is-joining-battle="pageController.pageState.value.isJoiningBattle"
          @join-battle="handleJoinBattle"
          @leave-battle="handleQuitBattle"
          @dismiss-battle="handleDismissBattle"
        />
      </div>
      <!-- 胜利者弹窗 -->
      <BattleWinnerModal
        v-if="canShowWinnerModal"
        :winner-data="winnerData"
        @close="handleCloseWinnerModal"
      />
    </div>

    <!-- 实时模式对战内容（已初始化后显示） -->
    <div v-if="pageController.pageState.value.isInitialized && unifiedIsRealtimeMode" class="battle-content">
      <div class="content-container">
        <!-- 对战头部信息 -->
        <BattleHeader
          :battle-data="unifiedBattleData"
          :is-user-creator="isUserCreator"
          :is-user-joined="isUserJoined"
          :is-battle-started="isBattleStarted"
          @start-battle="handleStartBattle"
          @leave="handleQuitBattle"
          @dismiss="handleDismissBattle"
        />

        <!-- 🔍 调试：显示当前状态 -->
        <div class="debug-status mb-4 p-4 bg-gray-800 rounded">
          <h4>调试信息：</h4>
          <p>状态: {{ unifiedBattleData?.state }}</p>
          <p>倒计时开始: {{ unifiedBattleData?.countdown_start }}</p>
          <p>玩家数: {{ unifiedBattleData?.joiner_count }}/{{ unifiedBattleData?.max_joiner }}</p>
          <p>实际玩家数组长度: {{ unifiedBattleData?.bets?.length || 0 }}</p>
          <p>是否倒计时: {{ isBattleCountdown }}</p>
          <p>是否可以加入: {{ canJoinBattle }}</p>
          <p>用户是否已加入: {{ isUserJoined }}</p>
          <p>是否正在加入: {{ pageController.pageState.value.isJoiningBattle }}</p>
        </div>

        <!-- 倒计时状态显示 -->
        <div v-if="isBattleCountdown" class="countdown-display">
          <div class="countdown-container">
            <div class="countdown-icon">
              <Icon name="heroicons:stopwatch" class="w-8 h-8 text-orange-400" />
            </div>
            <div class="countdown-content">
              <h3 class="countdown-title">{{ t('battle.state.countdown.title') }}</h3>
              <p class="countdown-description">{{ t('battle.state.countdown.description') }}</p>
              <div class="countdown-timer">
                <span class="countdown-text">{{ t('battle.state.countdown.badge') }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 对战状态显示 -->
        <BattleStateDisplay
          :battle-state="isBattleCountdown ? 'countdown' : battleStateString"
          :current-round="battleStateSync.currentRound"
          :total-rounds="battleStateSync.totalRounds"
          :opening-case-id="battleStateSync.openingCaseId || undefined"
          :is-battle-started="battleStateSync.isBattleStarted"
          :is-battle-finished="battleStateSync.isBattleFinished"
          :is-round-changing="battleStateSync.isRoundChanging"
          :completed-rounds="Array.from(battleStateSync.completedRounds)"
        />
        <!-- 箱子展示（实时模式，等待状态使用静态网格） -->
        <BattleCaseDisplay
          :cases="casesToShow"
          :current-round="battleStateSync.currentRound"
          :total-rounds="battleStateSync.totalRounds"
          :opening-case-id="battleStateSync.openingCaseId || undefined"
          :is-battle-started="battleStateSync.isBattleStarted"
          :is-battle-finished="battleStateSync.isBattleFinished"
          :is-round-changing="battleStateSync.isRoundChanging"
          :completed-cases="Array.from(battleStateSync.completedCases)"
        />

        <!-- 如果正在计算，显示计算进度（仅在对战开始后） -->
        <CalculationProgress
          v-if="isBattleStarted && showCalculationProgress"
          :progress="calculationProgress"
          :custom-steps="calculationSteps"
        />
        <!-- 玩家展示 - 根据模式传递不同数据 -->
        <BattlePlayerDisplay
          :players="unifiedBattleData.bets || []"
          :current-round="battleStateSync.currentRound"
          :total-rounds="battleStateSync.totalRounds"
          :max-players="unifiedBattleData.max_joiner || unifiedBattleData.bets.length"
          :is-battle-started="battleStateSync.isBattleStarted"
          :is-battle-finished="battleStateSync.isBattleFinished"
          :host-uid="unifiedBattleData.user?.uid"
          :is-user-creator="isUserCreator"
          :is-user-joined="isUserJoined"
          :current-user-id="currentUserId"
          :opening-player-name="battleStateSync.openingPlayerName"
          :opening-player-index="battleStateSync.openingPlayerIndex ?? undefined"
          :current-case-items="[...currentCaseItems]"
          :steps="calculationSteps"
          :can-join="canJoinBattle"
          :is-round-changing="battleStateSync.isRoundChanging"
          :player-records="battleStateSync.playerRecords"
          :completed-rounds="Array.from(battleStateSync.completedRounds)"
          :is-joining-battle="pageController.pageState.value.isJoiningBattle"
          @join-battle="handleJoinBattle"
          @leave-battle="handleQuitBattle"
          @dismiss-battle="handleDismissBattle"
        />
      </div>

      <!-- 胜利者弹窗 -->
      <BattleWinnerModal
        v-if="canShowWinnerModal"
        :winner-data="winnerData"
        @close="handleCloseWinnerModal"
      />

      <!-- 加入成功通知 -->
      <div 
        v-if="showJoinSuccessNotification"
        class="fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-3 animate-in slide-in-from-right duration-300"
      >
        <Icon name="heroicons:check-circle" class="w-5 h-5" />
        <span class="font-medium">{{ t('battle.join.success') }}</span>
      </div>

    </div>

    <!-- 加入成功通知（全局位置，适用于两种模式） -->
    <div 
      v-if="showJoinSuccessNotification"
      class="fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-3 animate-in slide-in-from-right duration-300"
    >
      <Icon name="heroicons:check-circle" class="w-5 h-5" />
      <span class="font-medium">{{ t('battle.join.success') || '加入对战成功！' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
// 🎯 Vue核心导入
import { watch, onMounted, onUnmounted, computed, ref, nextTick } from "vue"
// 引入智能连接策略及相关composables和store
import { useBattlePageController } from "~/composables/useBattlePageController"
import { useBattleDetailPage } from "~/composables/useBattleDetailPage"
import { useBattleCore } from "~/composables/useBattleCore"
import { useUserStore } from "~/stores/user"
import { useSocketRoomManager, socketRooms, socketEvents } from "~/utils/socket-manager"

// 🎯 国际化设置
const { t } = useI18n()

// 🎯 路由参数
const route = useRoute()
const battleId = computed(() => route.params.id as string)

// 🎯 导入BattleStatus枚举
import { BattleStatus } from "~/services/battle-api"

// 🎯 导入组件
import CalculationProgress from "~/components/ui/CalculationProgress.vue"
import BattleWinnerModal from "~/components/battle/BattleWinnerModal.vue"
import BattleDetailSkeleton from "~/components/battle/BattleDetailSkeleton.vue"
// Battle 组件
import BattleConnectionStatus from "~/components/battle/BattleConnectionStatus.vue"
import BattleStaticModeIndicator from "~/components/battle/BattleStaticModeIndicator.vue"
import BattleHeader from "~/components/battle/BattleHeader.vue"
import BattleStateDisplay from "~/components/battle/BattleStateDisplay.vue"
import BattleCaseDisplay from "~/components/battle/BattleCaseDisplay.vue"
import BattlePlayerDisplay from "~/components/battle/BattlePlayerDisplay.vue"


// 🎯 导入状态同步管理器
import { useBattleStateSync } from "~/composables/useBattleStateSync"

// 🎯 主要使用智能控制器
const pageController = useBattlePageController()
// 🎯 顶级调用所有 composables，避免在computed中调用
// 顶级使用 battleCore 和 pageData，可根据 unifiedIsRealtimeMode 使用对应数据
const battleCore = useBattleCore()
const pageData = useBattleDetailPage()
const socketRoomManager = useSocketRoomManager()

// 🎯 加入成功通知状态
const showJoinSuccessNotification = ref(false)

// 🎯 状态同步管理器
const {
  state: battleStateSync,
  updateBattleState,
  setOpeningState,
  addPlayerRecord,
  completeRound,
  switchToNextRound,
  finishBattle,
  resetBattleState,
  subscribeStateUpdate
} = useBattleStateSync()

// 🎯 动画系统初始化
const battleAnimations = useBattleAnimations()

// 🎯 监听状态更新事件
subscribeStateUpdate((event) => {
  switch (event.type) {
    case 'opening_start':
      break
    case 'opening_complete':
      // 可以在这里触发动画或其他UI更新
      break
    case 'round_complete':
      // 可以在这里处理轮次完成后的逻辑
      break
    case 'battle_end':
      // 可以在这里处理对战结束后的逻辑
      break
  }
})

// 🎯 创建统一的计算属性，避免双重数据源
const unifiedBattleData = computed(() => {
  const data = pageController.battleData.value
  return data
})
const unifiedCurrentRound = computed(() => pageController.currentRound.value)
const unifiedTotalRounds = computed(() => pageController.totalRounds.value)
const unifiedIsRealtimeMode = computed(() => pageController.isRealtimeMode.value)


// 连接状态封装，用于 BattleConnectionStatus
const connectionState = computed(() => {
  return unifiedIsRealtimeMode.value
    ? battleCore.battleWebSocket.connectionState.value
    : { isConnected: false, isReconnecting: false, reconnectAttempts: 0 }
})
// 重试连接处理
const handleRetryConnection = () => {
  if (unifiedIsRealtimeMode.value) {
    battleCore.initialize()
  }
}


// 保留获取状态字符串函数
const getBattleStateString = (state: number): string => {
  // 未开始/准备阶段
  if ([2,3,4].includes(state)) return 'waiting'
  switch (state) {
    case 1: return 'loading'
    case 5: return 'battle'
    case 11: return 'completed'
    case 20: return 'cancelled'
    default: return 'unknown'
  }
}


// 🎯 新增统一计算属性和事件处理
// 用户身份和状态
const isUserCreator = computed(() => {
  const user = useUserStore()
  return unifiedBattleData.value?.user?.uid === user.userId
})
const isUserJoined = computed(() => {
  const user = useUserStore()
  const bets = unifiedBattleData.value?.bets || []
  return bets.some((bet: any) => bet.user?.uid === user.userId)
})
// 当前用户ID
const currentUserId = computed(() => useUserStore().userId)
const isBattleStarted = computed(() => {
  const state = unifiedBattleData.value?.state
  return state !== undefined && state >= 5
})
const isBattleFinished = computed(() => {
  const state = unifiedBattleData.value?.state
  return state === 11 || state === 20
})

// 🎯 监听对战结束状态，控制动画启用
watch(isBattleFinished, (isFinished) => {
  if (isFinished) {
    battleAnimations.setAnimationEnabled(false)
  } else {
    battleAnimations.setAnimationEnabled(true)
  }
}, { immediate: true })
const isBattleCountdown = computed(() => {
  const state = unifiedBattleData.value?.state
  const countdownStart = unifiedBattleData.value?.countdown_start
  const joinerCount = unifiedBattleData.value?.joiner_count
  const maxJoiner = unifiedBattleData.value?.max_joiner

  // 🎯 临时修复：状态4且房间满员时显示倒计时
  const isRoomFull = joinerCount === maxJoiner && maxJoiner > 0
  const result = state === 4 && (countdownStart === true || isRoomFull)



  return result
})
const isBattleInProgress = computed(() => {
  const state = unifiedBattleData.value?.state
  return state !== undefined && [5,6,7,8,9,10].includes(state!)
})
const battleStateString = computed(() => getBattleStateString(unifiedBattleData.value?.state))

// 数据流相关
const roundResults = computed(() => {
  if (unifiedIsRealtimeMode.value) {
    // 实时模式：从 pageData 获取动态结果并打印调试信息
    const results = pageData.state.roundResults.value || []

    return results
  } else {
    // 静态模式：重构 API 数据
    const processStaticRoundResults = (battleData: any) => {
      if (!battleData || !battleData.bets) {
        return []
      }
      const allResults: any[] = []
      battleData.bets.forEach((bet: any, playerIndex: number) => {
        if (!bet.open_items || !Array.isArray(bet.open_items)) {
          return
        }
        bet.open_items.forEach((item: any, roundIndex: number) => {
          const round = roundIndex + 1
          const processedItem = {
            ...item,
            round,
            item_id: item.item_id || item.id || `static_${playerIndex}_${round}_${Date.now()}`,
            name: item.name || '未知物品',
            price: item.price || item.item_price?.price || 0,
            rarity_name: item.rarity_name || item.rarity || 'common',
            image: item.image || item.icon_url || '',
            isStatic: true,
            timestamp: item.timestamp || battleData.update_time
          }
          allResults.push({
            round,
            playerIndex,
            item: processedItem,
            user: bet.user,
            resultId: `static_${playerIndex}_${round}`,
            battleId: battleData.id,
            isComplete: true,
            displayOrder: round * 100 + playerIndex
          })
        })
      })
      allResults.sort((a, b) => a.displayOrder - b.displayOrder)
      return allResults
    }
    const staticResults = processStaticRoundResults(unifiedBattleData.value)
    return staticResults
  }
})

// 🎯 修复3：添加轮次结果显示状态管理
const roundResultsDisplay = computed(() => {
  const results = roundResults.value
  const totalRounds = unifiedTotalRounds.value
  const currentRound = unifiedCurrentRound.value
  const resultsByRound: Record<number, any[]> = {}
  results.forEach(r => {
    resultsByRound[r.round] = resultsByRound[r.round] || []
    resultsByRound[r.round].push(r)
  })
  const roundsDisplay: any[] = []
  for (let i = 1; i <= totalRounds; i++) {
    const list = resultsByRound[i] || []
    roundsDisplay.push({
      round: i,
      results: list,
      isCurrentRound: i === currentRound,
      isCompleted: list.length > 0,
      isVisible: unifiedIsRealtimeMode.value ? i <= currentRound : true,
      playerCount: list.length,
      totalValue: list.reduce((sum, r) => sum + (r.item?.price || 0), 0)
    })
  }
  return {
    byRound: resultsByRound,
    display: roundsDisplay,
    totalResults: results.length,
    completedRounds: roundsDisplay.filter(r => r.isCompleted).length
  }
})

// 🎯 修复4：添加轮次完成检测和通知机制
const lastCompletedRound = ref(0)
watch(
  () => roundResultsDisplay.value.completedRounds,
  (newCount, oldCount) => {
    if (newCount > oldCount && unifiedIsRealtimeMode.value) {
      handleRoundCompleted(newCount)
    }
  }
)

// 🎯 修复5：轮次完成处理函数
function handleRoundCompleted(round: number) {
  const roundData = roundResultsDisplay.value.byRound[round] || []
  if (!roundData.length) {
    return
  }
  window.dispatchEvent(new CustomEvent('battle:round_completed', {
    detail: {
      round,
      results: roundData,
      totalValue: roundData.reduce((sum, r) => sum + (r.item?.price || 0), 0),
      playerResults: roundData.map(r => ({ playerIndex: r.playerIndex, playerName: r.user?.nickname || `玩家${r.playerIndex+1}`, item: r.item, value: r.item?.price || 0 })),
      timestamp: Date.now(),
      battleId: unifiedBattleData.value?.id,
      isAllRoundsComplete: round === unifiedTotalRounds.value
    }
  }))
  lastCompletedRound.value = Math.max(lastCompletedRound.value, round)
  if (round === unifiedTotalRounds.value) {
    handleBattleCompleted()
  }
}

// 🎯 修复6：对战完成处理
function handleBattleCompleted() {
  setTimeout(() => {
    pageController.handleBattleAction('calculate-winner')
  }, 1000)
}

// 🎯 修复7：为调试面板添加轮次结果信息
const debugRoundResults = computed(() => {
  const d = roundResultsDisplay.value
  return {
    总结果数: d.totalResults,
    已完成轮次数: d.completedRounds,
    各轮次结果数: d.display.map(r => `R${r.round}:${r.playerCount}`).join(' ')
  }
})
// 🎯 原始数据流与交互计算属性和事件处理（恢复模板绑定）
// 案例条目数据
const currentCaseItems = computed(() =>
  unifiedIsRealtimeMode.value
    ? pageData.caseItems.currentCaseItems.value
    : pageController.currentCaseItems?.value || []
)
// 显示的箱子列表，根据模式区分
const displayCases = computed(() =>
  unifiedIsRealtimeMode.value
    ? battleCore.battleState.displayCases.value || []
    : pageController.displayCases?.value || []
)
// 静态模式下的网格展示案例
const staticDisplayCases = computed(() => {
  const rounds = unifiedBattleData.value?.rounds || []
  return rounds.map((r: any) => ({
    id: r.case.case_key,
    key: r.case.case_key,
    case_key: r.case.case_key,
    name: r.case.name,
    name_en: r.case.name_en,
    name_zh_hans: r.case.name_zh_hans,
    cover: r.case.cover,
    price: r.case.price,
    count: 1
  }))
})

// 🎯 等待状态下实时模式使用静态箱子网格
const casesToShow = computed(() => {
  if (unifiedIsRealtimeMode.value && !isBattleStarted.value) {
    return staticDisplayCases.value
  }
  return displayCases.value
})

// 当前开箱案例 ID
const openingCaseId = computed(() =>
  unifiedIsRealtimeMode.value
    ? battleCore.battleState.openingCaseId.value || undefined
    : undefined
)
// 当前开箱玩家名称
const openingPlayerName = computed(() =>
  unifiedIsRealtimeMode.value
    ? battleCore.battleState.openingPlayerName.value || ''
    : ''
)
// 当前开箱玩家索引
const openingPlayerIndex = computed(() =>
  unifiedIsRealtimeMode.value
    ? battleCore.battleState.openingPlayerIndex.value ?? undefined
    : undefined
)
// 是否显示计算进度
const showCalculationProgress = computed(() =>
  unifiedIsRealtimeMode.value
    ? pageData.state.showCalculationProgress.value
    : false
)
// 计算进度值
const calculationProgress = computed(() =>
  unifiedIsRealtimeMode.value
    ? pageData.state.calculationProgress.value
    : 0
)
// 计算步骤列表
const calculationSteps = computed(() =>
  unifiedIsRealtimeMode.value
    ? pageData.state.calculationSteps.value
    : []
)
// 弹窗与结果
const canShowWinnerModal = computed(() => pageController.canShowWinnerModal?.value || false)
const winnerData = computed(() => pageController.winnerData?.value)

// 🎯 计算是否允许加入对战
const canJoinBattle = computed(() => {
  // 如果用户已加入或是对战创建者，不允许加入
  if (isUserJoined.value || isUserCreator.value) {
    return false
  }
  
  // 如果对战已开始或已结束，不允许加入
  if (isBattleStarted.value || isBattleFinished.value) {
    return false
  }
  
  // 如果房间已满，不允许加入
  const currentPlayerCount = unifiedBattleData.value?.bets?.length || 0
  const maxPlayers = unifiedBattleData.value?.max_joiner || 4
  if (currentPlayerCount >= maxPlayers) {
    return false
  }
  
  // 如果对战状态不是等待状态，不允许加入
  const battleState = unifiedBattleData.value?.state
  if (battleState !== 2) { // 2 = WAITING
    return false
  }
  
  return true
})

// 统一事件处理
const handleJoinBattle = async () => {
  console.log('[Battle详情页] 开始加入对战流程')

  // 记录加入前的状态
  const beforeJoinPlayerCount = unifiedBattleData.value?.bets?.length || 0
  console.log('[Battle详情页] 加入前玩家数:', beforeJoinPlayerCount)

  // 执行加入对战操作
  await pageController.handleBattleAction('join-battle')

  // 等待一段时间确保数据更新完成
  await nextTick()

  // 检查数据是否更新
  setTimeout(() => {
    const afterJoinPlayerCount = unifiedBattleData.value?.bets?.length || 0
    console.log('[Battle详情页] 加入后玩家数:', afterJoinPlayerCount)

    if (afterJoinPlayerCount <= beforeJoinPlayerCount) {
      console.warn('[Battle详情页] 检测到数据可能未更新，尝试强制刷新')
      // 如果数据没有更新，可以考虑重新获取数据
      pageController.handleBattleAction('refresh-data')
    }
  }, 1000)
}
const handleStartBattle = () => pageController.handleBattleAction('start-battle')
const handleCloseWinnerModal = () => pageController.handleBattleAction('close-winner-modal')
const handleQuitBattle = () => {
  pageController.handleBattleAction('quit-battle')
}
const handleDismissBattle = () => {
  pageController.handleBattleAction('dismiss-battle')
}


// 🎯 状态同步初始化
const initializeBattleStateSync = () => {
  // 初始化状态
  updateBattleState({
    currentRound: unifiedCurrentRound.value,
    totalRounds: unifiedTotalRounds.value,
    isBattleStarted: isBattleStarted.value,
    isBattleFinished: isBattleFinished.value
  })

  // 设置开箱状态
  if (openingCaseId.value) {
    setOpeningState(
      openingCaseId.value,
      openingPlayerIndex.value ?? null,
      openingPlayerName.value
    )
  }
}

// 🎯 监听状态变化并同步到状态管理器
watch([unifiedCurrentRound, unifiedTotalRounds, isBattleStarted, isBattleFinished],
  ([currentRound, totalRounds, battleStarted, battleFinished]) => {
    updateBattleState({
      currentRound,
      totalRounds,
      isBattleStarted: battleStarted,
      isBattleFinished: battleFinished
    })
  }
)

// 🎯 监听开箱状态变化
watch([openingCaseId, openingPlayerIndex, openingPlayerName],
  ([caseId, playerIndex, playerName]) => {
    if (caseId) {
      setOpeningState(
        caseId,
        playerIndex ?? null,
        playerName
      )
    }
  }
)

// 🎯 监听当前轮次物品变化，用于记录玩家开箱结果
watch(currentCaseItems, (newItems, oldItems) => {
  if (!newItems || newItems.length === 0) return

  // 检查是否有新的物品添加
  const newItemsCount = newItems.length
  const oldItemsCount = oldItems?.length || 0

  if (newItemsCount > oldItemsCount) {
    // 有新物品，记录到对应玩家
    const newItem = newItems[newItemsCount - 1]
    const currentPlayerIndex = openingPlayerIndex.value

    if (newItem && currentPlayerIndex !== null && currentPlayerIndex !== undefined) {
      const players = unifiedBattleData.value?.bets || []
      const currentPlayer = players[currentPlayerIndex]

      if (currentPlayer) {
        // 添加到状态管理器
        addPlayerRecord(currentPlayer.user.uid, newItem, battleStateSync.currentRound - 1)
      }
    }
  }
}, { deep: true })

// 🎯 监听轮次变化，自动推进状态
watch([unifiedCurrentRound, unifiedTotalRounds], ([currentRound, totalRounds]) => {
  // 如果轮次发生变化，检查是否需要完成上一轮
  if (currentRound > battleStateSync.currentRound) {
    const previousRound = battleStateSync.currentRound

    // 完成上一轮
    if (previousRound > 0) {
      completeRound(previousRound, battleStateSync.openingCaseId || undefined)
    }

    // 如果对战已结束
    if (currentRound > totalRounds && isBattleFinished.value) {
      finishBattle()
    }
  }
})

// 🎯 监听对战结束状态
watch(isBattleFinished, (isFinished) => {
  if (isFinished && !battleStateSync.isBattleFinished) {
    finishBattle()
  }
})

// 🎯 轮询机制（WebSocket备选方案）
let pollingTimer: NodeJS.Timeout | null = null

const startPolling = () => {
  if (pollingTimer) return // 避免重复启动

  pollingTimer = setInterval(async () => {
    try {
      const currentState = pageController.battleData.value?.state
      await pageController.initializeBattleMode(battleId.value)
      const newState = pageController.battleData.value?.state

      if (newState !== currentState) {
        console.warn('[🎰BATTLE-PAGE] 🔄 轮询检测到状态变化:', {
          oldState: currentState,
          newState: newState,
          battleId: battleId.value
        })

        // 如果状态变为5（进行中），停止轮询
        if (newState === 5) {
          stopPolling()
        }
      }
    } catch (error) {
      console.error('[🎰BATTLE-PAGE] 轮询检查失败:', error)
    }
  }, 3000) // 每3秒检查一次
}

const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
    console.warn('[🎰BATTLE-PAGE] 🛑 停止状态轮询')
  }
}

// Socket事件处理函数 - 增强版本
const handleBattleUpdate = (event: Event) => {
  try {
    const customEvent = event as CustomEvent
    const { action, data } = customEvent.detail

    console.log(`[Battle详情页] 收到对战更新 - ${battleId.value}:`, action, data)

    // 验证数据有效性
    if (!data || !data.uid) {
      console.warn('[Battle详情页] 对战更新数据无效:', data)
      return
    }

    // 确保是当前对战的更新
    if (data.uid === battleId.value) {
      console.log(`[Battle详情页] 处理对战状态更新:`, {
        oldState: unifiedBattleData.value?.state,
        newState: data.state,
        action
      })

      // 更新页面控制器的数据
      pageController.updateBattleData(data)

      // 同步到状态管理器
      updateBattleState({
        currentRound: data.current_round || unifiedCurrentRound.value,
        totalRounds: data.total_rounds || unifiedTotalRounds.value,
        isBattleStarted: data.state >= 5,
        isBattleFinished: data.state === 11 || data.state === 20
      })

      // 触发页面更新事件
      window.dispatchEvent(new CustomEvent('battle:state_updated', {
        detail: { battleId: battleId.value, state: data.state, action }
      }))
    }
  } catch (error) {
    console.error('[Battle详情页] 处理对战更新失败:', error)
  }
}

const handleBattleStart = (event: Event) => {
  try {
    const customEvent = event as CustomEvent
    const data = customEvent.detail?.data

    console.log(`[Battle详情页] 收到对战开始 - ${battleId.value}:`, data)

    if (data && data.uid === battleId.value) {
      console.log(`[Battle详情页] 对战正式开始，状态: ${data.state}`)

      // 更新页面控制器的数据
      pageController.updateBattleData(data)

      // 同步到状态管理器
      updateBattleState({
        currentRound: data.current_round || 1,
        totalRounds: data.total_rounds || unifiedTotalRounds.value,
        isBattleStarted: true,
        isBattleFinished: false
      })

      // 如果是实时模式，初始化实时同步
      if (unifiedIsRealtimeMode.value) {
        battleCore.initialize()
      }

      // 触发对战开始事件
      window.dispatchEvent(new CustomEvent('battle:started', {
        detail: { battleId: battleId.value, data }
      }))
    }
  } catch (error) {
    console.error('[Battle详情页] 处理对战开始失败:', error)
  }
}

const handleBattleEnd = (event: Event) => {
  try {
    const customEvent = event as CustomEvent
    const data = customEvent.detail?.data

    console.log(`[Battle详情页] 收到对战结束 - ${battleId.value}:`, data)

    if (data && data.uid === battleId.value) {
      console.log(`[Battle详情页] 对战结束，最终状态: ${data.state}`)

      // 更新页面控制器的数据
      pageController.updateBattleData(data)

      // 完成对战状态
      finishBattle()

      // 触发对战结束事件
      window.dispatchEvent(new CustomEvent('battle:ended', {
        detail: { battleId: battleId.value, data }
      }))

      // 延迟显示胜利者弹窗
      setTimeout(() => {
        pageController.handleBattleAction('calculate-winner')
      }, 2000)
    }
  } catch (error) {
    console.error('[Battle详情页] 处理对战结束失败:', error)
  }
}

// 处理玩家加入事件
const handlePlayerJoined = (event: Event) => {
  const customEvent = event as CustomEvent
  const { battleId: eventBattleId, data, newPlayer, success } = customEvent.detail || {}

  if (eventBattleId === battleId.value) {
    console.log(`[Battle详情页] 收到玩家加入事件:`, { newPlayer, data, success })

    if (success) {
      // 使用控制器的更新方法更新对战数据
      pageController.updateBattleData(data)
      console.log(`[Battle详情页] 玩家加入后数据已更新，当前玩家数: ${data.bets?.length || 0}`)

      // 强制触发响应式更新
      nextTick(() => {
        console.log(`[Battle详情页] 强制刷新完成，最终玩家数: ${unifiedBattleData.value?.bets?.length || 0}`)

        // 显示加入成功通知
        showJoinSuccessNotification.value = true
        setTimeout(() => {
          showJoinSuccessNotification.value = false
        }, 3000)
      })
    }
  }
}

// 处理玩家加入失败事件
const handlePlayerJoinFailed = (event: Event) => {
  const customEvent = event as CustomEvent
  const { battleId: eventBattleId, error } = customEvent.detail || {}

  if (eventBattleId === battleId.value) {
    console.error(`[Battle详情页] 玩家加入失败:`, error)
    // 可以在这里显示错误提示
  }
}

// 处理对战轮次更新
const handleBattleRoundUpdate = (event: Event) => {
  try {
    const customEvent = event as CustomEvent
    const data = customEvent.detail?.data

    console.log(`[Battle详情页] 收到轮次更新 - ${battleId.value}:`, data)

    if (data && data.battle_id === battleId.value) {
      // 更新当前轮次
      if (data.round !== undefined) {
        switchToNextRound()
      }

      // 如果有开箱结果，记录到状态管理器
      if (data.results && Array.isArray(data.results)) {
        data.results.forEach((result: any) => {
          if (result.player_id && result.item) {
            addPlayerRecord(result.player_id, result.item, data.round - 1)
          }
        })
      }
    }
  } catch (error) {
    console.error('[Battle详情页] 处理轮次更新失败:', error)
  }
}

// 🔥 新增：使用新的WebSocket处理器
const battleWS = useBattleWebSocket(battleId.value)

// 初始化Socket监听器 - 增强版本
const initializeBattleSocket = () => {
  // 🔥 修复：注册新的WebSocket回调处理房间更新
  battleWS.onRoomUpdate((data: any) => {
    console.log('[🎰BATTLE-PAGE] 收到房间更新:', data)

    // 检查是否是当前房间的更新
    if (data.uid === battleId.value || data.short_id === battleId.value) {
      // 更新统一数据
      if (unifiedBattleData.value) {
        Object.assign(unifiedBattleData.value, data)
      }

      // 🔥 关键：处理倒计时开始
      if (data.countdown_start === true && data.state === 4) {
        console.log('[🎰BATTLE-PAGE] 🚀 倒计时开始！')
        // 触发倒计时状态更新
        nextTick(() => {
          // 强制重新计算倒计时状态
          console.log('[🎰BATTLE-PAGE] 倒计时状态:', isBattleCountdown.value)
        })
      }

      // 处理房间状态变化
      handleBattleUpdate(data)
    }
  })

  battleWS.onBattleStart((data: any) => {
    console.log('[🎰BATTLE-PAGE] 收到对战开始:', data)
    handleBattleStart(data)
  })

  battleWS.onBattleEnd((data: any) => {
    console.log('[🎰BATTLE-PAGE] 收到对战结束:', data)
    handleBattleEnd(data)
  })

  battleWS.onRoundStart((data: any) => {
    console.log('[🎰BATTLE-PAGE] 收到轮次开始:', data)
    handleBattleRoundUpdate(data)
  })

  // 🔥 保留旧的监听器作为备用
  // 加入对战房间
  socketRoomManager.joinRoom(socketRooms.battle(battleId.value))

  // 监听对战事件
  socketRoomManager.addEventListener(socketEvents.battle.update, handleBattleUpdate)
  socketRoomManager.addEventListener(socketEvents.battle.start, handleBattleStart)
  socketRoomManager.addEventListener(socketEvents.battle.end, handleBattleEnd)
  socketRoomManager.addEventListener(socketEvents.battle.roundUpdate, handleBattleRoundUpdate)

  // 监听页面级别的对战事件
  window.addEventListener('battle:state_updated', (event: Event) => {
    const customEvent = event as CustomEvent
    const { battleId: eventBattleId, state, action } = customEvent.detail || {}

    if (eventBattleId === battleId.value) {
      console.log(`[Battle详情页] 收到对战状态更新事件: ${state}, 动作: ${action}`)
      // 可以在这里添加额外的UI更新逻辑
    }
  })

  window.addEventListener('battle:started', (event: Event) => {
    const customEvent = event as CustomEvent
    const { battleId: eventBattleId } = customEvent.detail || {}

    if (eventBattleId === battleId.value) {
      console.log(`[Battle详情页] 收到对战开始事件`)
      // 停止轮询，切换到实时模式
      stopPolling()
    }
  })

  window.addEventListener('battle:ended', (event: Event) => {
    const customEvent = event as CustomEvent
    const { battleId: eventBattleId } = customEvent.detail || {}

    if (eventBattleId === battleId.value) {
      console.log(`[Battle详情页] 收到对战结束事件`)
      // 可以在这里添加对战结束后的清理逻辑
    }
  })

  // 监听玩家加入事件
  window.addEventListener('battle:player_joined', handlePlayerJoined)
  
  // 监听玩家加入失败事件
  window.addEventListener('battle:player_join_failed', handlePlayerJoinFailed)

  console.log(`[Battle详情页] Socket监听器已设置，监听对战: ${battleId.value}`)
}

// 🎯 页面初始化
onMounted(async () => {
  console.log('[🎰BATTLE-PAGE] 页面挂载，初始化智能连接策略')

  if (battleId.value) {
    try {
      // 初始化Socket监听器
      initializeBattleSocket()

      await pageController.initializeBattleMode(battleId.value)
      console.warn('[🎰BATTLE-PAGE] 对战状态:', {
        battleId: battleId.value,
        mode: pageController.isRealtimeMode.value ? 'Realtime' : 'Static',
        battleState: pageController.battleData.value?.state,
        currentRound: pageController.currentRound.value,
        totalRounds: pageController.totalRounds.value,
        hasCalculated: pageController.hasCalculated.value
      })

      // 🎯 专注解决WebSocket问题，暂时移除轮询

      // 初始化状态同步
      await nextTick()
      initializeBattleStateSync()

    } catch (error) {
      console.error('[🎰BATTLE-PAGE] 智能连接策略初始化失败:', error)
    }
  }
})

// 🎯 调试：监听showWinnerModal变化
watch(() => pageController.canShowWinnerModal.value, (newValue) => {
  console.log('[🎰BATTLE-PAGE] canShowWinnerModal变化:', newValue)
})

// 🎯 调试：监听对战数据变化
watch(() => unifiedBattleData.value?.bets?.length, (newLength, oldLength) => {
  console.log('[🎰BATTLE-PAGE] 玩家数量变化:', { oldLength, newLength })
}, { immediate: true })

// 🎯 调试：监听用户加入状态变化
watch(() => isUserJoined.value, (newValue, oldValue) => {
  console.log('[🎰BATTLE-PAGE] 用户加入状态变化:', { oldValue, newValue })
}, { immediate: true })

// 🧹 页面卸载清理
onUnmounted(() => {
  console.log('[🎰BATTLE-PAGE] 页面卸载，清理资源')

  // 清理轮询
  stopPolling()

  // 🔥 清理新的WebSocket监听器
  battleWS.cleanup()

  // 清理Socket房间订阅
  socketRoomManager.cleanup()

  // 清理页面级别事件监听器
  window.removeEventListener('battle:player_joined', handlePlayerJoined)
  window.removeEventListener('battle:player_join_failed', handlePlayerJoinFailed)

  console.log(`[Battle详情页] 已清理Socket订阅和事件监听器 - ${battleId.value}`)
})

// 🎯 页面元数据
useHead({
  title: computed(() => {
    const battleData = unifiedBattleData.value
    return battleData ? `${t('battle.detail')} - ${battleData.name || battleData.id}` : t('battle.detail')
  })
})
</script>

<style scoped>
.countdown-display {
  margin-bottom: 1.5rem;
}

.countdown-container {
  background: linear-gradient(to right, rgba(249, 115, 22, 0.1), rgba(234, 179, 8, 0.1));
  border: 1px solid rgba(249, 115, 22, 0.2);
  border-radius: 0.75rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.countdown-icon {
  flex-shrink: 0;
}

.countdown-content {
  flex: 1;
}

.countdown-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #fb923c;
  margin-bottom: 0.25rem;
}

.countdown-description {
  font-size: 0.875rem;
  color: #9ca3af;
  margin-bottom: 0.5rem;
}

.countdown-timer .countdown-text {
  color: #fdba74;
  font-weight: 500;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.battle-detail-page {
  min-height: 100vh;
  /* background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #0f1419 100%); */
  display: flex;
  flex-direction: column;
  color: white;
  position: relative;
}

/* 内容区域样式 */
.battle-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
}

.content-container {
  /* max-width: 1200px; */
  margin: 0 auto;
  /* padding: 0 1rem; */
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 加载和错误状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  padding: 2rem;
}

.error-content {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .battle-content {
    padding: 0.5rem 0;
  }
  
  .content-container {
    padding: 0 0.75rem;
    gap: 1rem;
  }
}


/* 返回按钮样式 */
.fixed .bg-gray-800\/90 {
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.fixed .bg-gray-800\/90 a {
  transition: all 0.3s ease;
  border-radius: 0.5rem;
}

.fixed .bg-gray-800\/90 a:hover {
  background: linear-gradient(
    135deg,
    rgba(0, 168, 255, 0.1) 0%,
    rgba(255, 105, 180, 0.1) 100%
  );
  transform: translateY(-2px);
  box-shadow: 
    0 0.5rem 1rem rgba(0, 0, 0, 0.3),
    0 0 0.5rem rgba(0, 168, 255, 0.2);
}

.fixed .bg-gray-800\/90 a:active {
  transform: translateY(0);
}
</style>
