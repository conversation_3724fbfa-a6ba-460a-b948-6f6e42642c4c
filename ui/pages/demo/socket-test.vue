<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 p-6">
    <div class="max-w-4xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-4">🔌 Socket.IO 连接测试</h1>
        <p class="text-gray-300 text-lg">测试 Socket.IO 基本连接功能，验证代理配置和缓存问题</p>
        <div class="mt-4">
          <NuxtLink to="/demo" class="nav-button">
            <i class="fas fa-arrow-left mr-2"></i>
            返回演示中心
          </NuxtLink>
        </div>
      </div>

      <div class="space-y-6">
        <div class="demo-card border-l-4 border-red-500">
          <h2 class="demo-title text-red-400">⚠️ 重要提示</h2>
          <div class="text-gray-300 space-y-2">
            <p>如果您仍然看到 CORS 错误，请：</p>
            <ul class="list-disc list-inside space-y-1 text-sm">
              <li>硬刷新浏览器 (Ctrl+Shift+R 或 Cmd+Shift+R)</li>
              <li>清除浏览器缓存</li>
              <li>或者打开隐私/无痕浏览窗口</li>
            </ul>
          </div>
        </div>

        <div class="demo-card">
          <h2 class="demo-title">⚙️ 当前配置</h2>
          <div class="text-sm font-mono text-gray-300 space-y-1">
            <div>Socket URL: <span class="text-primary">{{ socketUrl }}</span></div>
            <div>开发环境: <span class="text-primary">{{ isDev }}</span></div>
            <div>EIO 版本: <span class="text-primary">4</span></div>
            <div>传输方式: <span class="text-primary">polling, websocket</span></div>
            <div>路径: <span class="text-primary">/socket.io/</span></div>
          </div>
        </div>

        <div class="demo-card">
          <h2 class="demo-title">📊 连接状态</h2>
          <div :class="connectionStatus.color" class="text-sm font-mono font-semibold">
            {{ connectionStatus.text }}
          </div>
        </div>

        <div class="demo-card">
          <h2 class="demo-title">📝 连接日志</h2>
          <div class="text-sm font-mono max-h-40 overflow-y-auto space-y-1">
            <div v-for="(log, index) in logs" :key="index" class="text-gray-300 border-b border-gray-600 pb-1">
              {{ log }}
            </div>
            <div v-if="logs.length === 0" class="text-gray-500 text-center py-4">
              <div class="text-2xl mb-2">📋</div>
              暂无日志...
            </div>
          </div>
        </div>

        <div class="demo-card">
          <h2 class="demo-title">🎮 测试操作</h2>
          <div class="flex flex-wrap gap-3">
            <button @click="testConnection" class="demo-button demo-button-primary">
              🔌 强制重新连接
            </button>
            <button @click="clearLogs" class="demo-button demo-button-secondary">
              🗑️ 清空日志
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const config = useRuntimeConfig()
const logs = ref<string[]>([])
const isConnected = ref(false)
const socketUrl = ref('')
const isDev = ref(false)

// 定时器引用，用于清理
let disconnectTimer: NodeJS.Timeout | null = null
let initTimer: NodeJS.Timeout | null = null

const connectionStatus = computed(() => {
  if (isConnected.value) {
    return { text: '✅ 已连接', color: 'text-green-600' }
  } else {
    return { text: '❌ 未连接', color: 'text-red-600' }
  }
})

function addLog(message: string) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push(`[${timestamp}] ${message}`)
  if (logs.value.length > 20) {
    logs.value.shift()
  }
}

function clearLogs() {
  logs.value = []
}

async function testConnection() {
  try {
    addLog('🚀 开始强制 Socket.IO 连接测试...')
    
    // 强制重新导入 Socket.IO
    const io = (await import('socket.io-client')).default
    addLog('✅ Socket.IO 客户端重新导入成功')
    
    // 检查环境
    isDev.value = process.env.NODE_ENV === 'development'

    // 开发环境使用本地代理，生产环境使用服务器地址
    const url = isDev.value
      ? window.location.origin  // 开发环境使用本地代理
      : (config.public.socketUrl || 'https://socket.cs2.net.cn')
    socketUrl.value = url

    addLog(`🔗 连接地址: ${url}`)
    addLog(`🔧 开发环境: ${isDev.value}`)
    addLog(`🔧 EIO 版本: 4`)
    
    // 创建连接 - 强制使用正确的配置
    const socket = io(url, {
      transports: ['polling', 'websocket'],
      forceNew: true,  // 强制新连接
      reconnection: false,
      timeout: 10000,
      autoConnect: true,
      path: '/socket.io/',
      query: {
        page: 'ws',
        EIO: '4',  // 强制 EIO v4
        t: Date.now().toString()  // 添加时间戳避免缓存
      },
      upgrade: true,
      rememberUpgrade: false
    })
    
    addLog('🔌 Socket.IO 实例创建完成，强制使用新配置')
    
    socket.on('connect', () => {
      isConnected.value = true
      addLog(`🎉 连接成功! Socket ID: ${socket.id}`)
      const transportName = (socket as any).io?.engine?.transport?.name || 'unknown'
      addLog(`🚀 传输方式: ${transportName}`)
      const actualUri = (socket as any).io?.uri || 'unknown'
      addLog(`🌐 实际连接地址: ${actualUri}`)
    })
    
    socket.on('disconnect', (reason: any) => {
      isConnected.value = false
      addLog(`🔴 连接断开: ${reason}`)
    })
    
    socket.on('connect_error', (error: any) => {
      isConnected.value = false
      addLog(`❌ 连接错误: ${error.message}`)
      addLog(`❌ 错误详情: ${error.description || 'N/A'}`)
      addLog(`❌ 错误类型: ${error.type || 'N/A'}`)
      
      // 检查是否仍在使用旧配置
      if (error.message && error.message.includes('socket.cs2.net.cn')) {
        addLog('⚠️ 检测到仍在使用旧配置！请清除浏览器缓存！')
      }
    })
    
    // 10秒后断开连接
    disconnectTimer = setTimeout(() => {
      if (socket.connected) {
        socket.disconnect()
        addLog('🔌 主动断开连接')
      }
    }, 10000)
    
  } catch (error: any) {
    addLog(`💥 测试失败: ${error.message}`)
  }
}

// 页面加载时自动测试
onMounted(() => {
  addLog('📄 页面加载完成，开始自动测试...')
  initTimer = setTimeout(() => {
    testConnection()
  }, 1000)
})

// 组件卸载时清理定时器和连接
onUnmounted(() => {
  if (disconnectTimer) {
    clearTimeout(disconnectTimer)
    disconnectTimer = null
  }

  if (initTimer) {
    clearTimeout(initTimer)
    initTimer = null
  }

  // 如果还有活跃的连接，断开它
  if (isConnected.value) {
    addLog('🔌 组件卸载，断开连接')
  }
})
</script>
