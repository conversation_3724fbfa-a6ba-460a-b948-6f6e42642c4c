<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 p-6">
    <div class="max-w-6xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-4">🔍 Socket.IO 事件调试</h1>
        <p class="text-gray-300 text-lg">实时监控 Socket.IO 连接状态和事件接收情况</p>
        <div class="mt-4">
          <NuxtLink to="/demo" class="nav-button">
            <i class="fas fa-arrow-left mr-2"></i>
            返回演示中心
          </NuxtLink>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 连接状态 -->
        <div class="demo-card">
          <h2 class="demo-title">🔌 连接状态</h2>
          <div class="text-sm space-y-2 text-gray-300">
            <div :class="socketStore.isConnected ? 'text-green-400' : 'text-red-400'" class="flex items-center">
              <span class="mr-2">{{ socketStore.isConnected ? '✅' : '❌' }}</span>
              状态: {{ socketStore.isConnected ? '已连接' : '未连接' }}
            </div>
            <div v-if="socketStore.socketId" class="flex items-center">
              <span class="mr-2">🆔</span>
              Socket ID: <code class="ml-1 text-primary">{{ socketStore.socketId }}</code>
            </div>
            <div class="flex items-center">
              <span class="mr-2">📨</span>
              消息接收数: <span class="text-primary">{{ socketStore.statistics.messagesReceived }}</span>
            </div>
            <div class="flex items-center">
              <span class="mr-2">❌</span>
              错误数: <span class="text-red-400">{{ socketStore.statistics.errors }}</span>
            </div>
            <div v-if="socketStore.lastMessage" class="flex items-center">
              <span class="mr-2">⏰</span>
              最后消息: <span class="text-gray-400">{{ formatTime(socketStore.lastMessage) }}</span>
            </div>
          </div>
        </div>

        <!-- 控制面板 -->
        <div class="demo-card">
          <h2 class="demo-title">🎮 控制面板</h2>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
            <button @click="requestStats" class="demo-button demo-button-primary">
              📊 请求统计数据
            </button>
            <button @click="requestCaseRecords" class="demo-button demo-button-success">
              🎰 请求开箱记录
            </button>
            <button @click="joinMonitor" class="demo-button demo-button-purple">
              📡 加入监控频道
            </button>
            <button @click="testDirectConnection" class="demo-button demo-button-warning">
              🔗 测试直连后端
            </button>
            <button @click="clearEvents" class="demo-button demo-button-secondary">
              🗑️ 清空事件
            </button>
          </div>
        </div>

        <!-- 接收到的事件 -->
        <div class="demo-card lg:col-span-2">
          <h2 class="demo-title">📨 接收到的事件 (最近20条)</h2>
          <div class="text-sm font-mono max-h-60 overflow-y-auto space-y-2">
            <div v-for="(event, index) in events" :key="index" class="border border-gray-600 rounded p-3 bg-gray-800">
              <div class="text-primary font-semibold mb-1">
                [{{ formatTime(event.timestamp) }}] {{ event.type }}
              </div>
              <div class="text-gray-300 ml-2 text-xs">
                <pre class="whitespace-pre-wrap">{{ JSON.stringify(event.data, null, 2) }}</pre>
              </div>
            </div>
            <div v-if="events.length === 0" class="text-gray-500 text-center py-8">
              <div class="text-4xl mb-2">📭</div>
              暂无事件接收...
            </div>
          </div>
        </div>

        <!-- 发送的消息 -->
        <div class="demo-card lg:col-span-2">
          <h2 class="demo-title">📤 发送的消息</h2>
          <div class="text-sm font-mono max-h-40 overflow-y-auto space-y-2">
            <div v-for="(msg, index) in sentMessages" :key="index" class="border border-gray-600 rounded p-3 bg-gray-800">
              <div class="text-orange-400 font-semibold">
                [{{ formatTime(msg.timestamp) }}] {{ msg.event }}
              </div>
              <div class="text-gray-300 ml-2 text-xs">
                → {{ JSON.stringify(msg.data) }}
              </div>
            </div>
            <div v-if="sentMessages.length === 0" class="text-gray-500 text-center py-8">
              <div class="text-4xl mb-2">📤</div>
              暂无发送消息...
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const socketStore = useSocketStore()
const events = ref<any[]>([])
const sentMessages = ref<any[]>([])

// 监听所有 Socket 事件
const eventListeners: (() => void)[] = []

// 定时器引用，用于清理
let disconnectTimer: NodeJS.Timeout | null = null
let initTimer: NodeJS.Timeout | null = null

function addEvent(type: string, data: any) {
  events.value.unshift({
    type,
    data,
    timestamp: new Date()
  })
  
  // 只保留最近20条
  if (events.value.length > 20) {
    events.value = events.value.slice(0, 20)
  }
}

function addSentMessage(event: string, data: any) {
  sentMessages.value.unshift({
    event,
    data,
    timestamp: new Date()
  })
  
  // 只保留最近10条
  if (sentMessages.value.length > 10) {
    sentMessages.value = sentMessages.value.slice(0, 10)
  }
}

function formatTime(date: Date | string | number) {
  const d = new Date(date)
  return d.toLocaleTimeString()
}

function clearEvents() {
  events.value = []
  sentMessages.value = []
}

// 控制函数
function requestStats() {
  if (socketStore.socket) {
    socketStore.socket.emit('monitor', ['get_stats'])
    addSentMessage('monitor', ['get_stats'])
  }
}

function requestCaseRecords() {
  if (socketStore.socket) {
    socketStore.socket.emit('monitor', ['case_records'])
    addSentMessage('monitor', ['case_records'])
  }
}

function joinMonitor() {
  if (socketStore.socket) {
    socketStore.socket.emit('join', 'monitor')
    addSentMessage('join', 'monitor')
  }
}

async function testDirectConnection() {
  try {
    addEvent('测试', '开始直连后端测试...')

    // 动态导入 Socket.IO
    const io = (await import('socket.io-client')).default

    // 使用与主应用相同的配置（开发环境使用代理）
    const isDev = process.env.NODE_ENV === 'development'
    const socketUrl = isDev ? window.location.origin : 'https://socket.cs2.net.cn'

    const testSocket = io(socketUrl, {
      transports: ['polling', 'websocket'],
      timeout: 10000,
      forceNew: true,
      path: '/socket.io/',
      query: {
        page: 'ws',
        EIO: '4'
      }
    })

    testSocket.on('connect', () => {
      addEvent('直连测试', `连接成功! Socket ID: ${testSocket.id}`)

      // 发送测试消息
      testSocket.emit('join', 'monitor')
      testSocket.emit('monitor', ['get_stats'])
      testSocket.emit('join', 'ws_channel')

      addEvent('直连测试', '已发送测试消息')
    })

    testSocket.on('disconnect', (reason: any) => {
      addEvent('直连测试', `连接断开: ${reason}`)
    })

    testSocket.on('connect_error', (error: any) => {
      addEvent('直连测试', `连接错误: ${error.message}`)
    })

    // 监听所有可能的事件
    const events = ['message', 'ws_channel', 'monitor', 'case_records', 'data', 'update', 'stats']
    events.forEach(eventName => {
      testSocket.on(eventName, (data: any) => {
        addEvent(`直连-${eventName}`, data)
      })
    })

    // 使用 onAny 捕获所有事件
    if (typeof (testSocket as any).onAny === 'function') {
      (testSocket as any).onAny((eventName: string, ...args: any[]) => {
        addEvent(`直连-ANY-${eventName}`, args[0])
      })
    }

    // 5秒后断开
    disconnectTimer = setTimeout(() => {
      testSocket.disconnect()
      addEvent('直连测试', '主动断开连接')
    }, 5000)

  } catch (error: any) {
    addEvent('直连测试', `错误: ${error.message}`)
  }
}

onMounted(() => {
  // 监听所有可能的 Socket 事件
  const socketEvents = [
    'socket:connected',
    'socket:disconnected', 
    'socket:message',
    'socket:monitor_update',
    'socket:case_records_update',
    'socket:case_opened',
    'socket:room_created',
    'socket:room_updated'
  ]

  socketEvents.forEach(eventName => {
    const listener = (event: CustomEvent) => {
      addEvent(eventName, event.detail)
    }
    window.addEventListener(eventName, listener as EventListener)
    eventListeners.push(() => window.removeEventListener(eventName, listener as EventListener))
  })

  // 如果已经连接，立即请求数据
  if (socketStore.isConnected) {
    initTimer = setTimeout(() => {
      requestStats()
      requestCaseRecords()
    }, 1000)
  }
})

onUnmounted(() => {
  // 清理事件监听器
  eventListeners.forEach(cleanup => cleanup())

  // 清理定时器
  if (disconnectTimer) {
    clearTimeout(disconnectTimer)
    disconnectTimer = null
  }

  if (initTimer) {
    clearTimeout(initTimer)
    initTimer = null
  }
})
</script>
