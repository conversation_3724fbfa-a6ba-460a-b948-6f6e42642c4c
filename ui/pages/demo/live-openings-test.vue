<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 p-6">
    <div class="max-w-6xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-4">🎰 LiveOpenings 组件测试</h1>
        <p class="text-gray-300 text-lg">测试 LiveOpenings 组件功能，使用模拟数据验证组件渲染和实时更新</p>
        <div class="mt-4">
          <NuxtLink to="/demo" class="nav-button">
            <i class="fas fa-arrow-left mr-2"></i>
            返回演示中心
          </NuxtLink>
        </div>
      </div>

      <div class="space-y-6">
        <!-- 控制面板 -->
        <div class="demo-card">
          <h2 class="demo-title">🎮 测试控制</h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <button @click="loadMockData" class="demo-button demo-button-primary">
              📊 加载模拟数据
            </button>
            <button @click="addNewRecord" class="demo-button demo-button-success">
              ➕ 添加新记录
            </button>
            <button @click="clearData" class="demo-button demo-button-danger">
              🗑️ 清空数据
            </button>
            <button @click="testRealApi" class="demo-button demo-button-purple">
              🌐 测试真实 API
            </button>
          </div>
        </div>

        <!-- Socket Store 状态 -->
        <div class="demo-card">
          <h2 class="demo-title">📊 Socket Store 状态</h2>
          <div class="text-sm space-y-2 text-gray-300">
            <div class="flex items-center">
              <span class="mr-2">{{ socketStore.isConnected ? '✅' : '❌' }}</span>
              连接状态: {{ socketStore.isConnected ? '已连接' : '未连接' }}
            </div>
            <div class="flex items-center">
              <span class="mr-2">📦</span>
              开箱记录数量: <span class="text-primary ml-1">{{ socketStore.caseRecords.length }}</span>
            </div>
            <div v-if="socketStore.caseRecords.length > 0" class="text-xs">
              <span class="mr-2">📝</span>
              最新记录: <code class="text-gray-400">{{ JSON.stringify(socketStore.caseRecords[0]).substring(0, 100) }}...</code>
            </div>
          </div>
        </div>

        <!-- LiveOpenings 组件 -->
        <div class="demo-card">
          <h2 class="demo-title">🎰 LiveOpenings 组件</h2>
          <div class="bg-gray-900 p-4 rounded">
            <LiveOpenings />
          </div>
        </div>

        <!-- API 测试结果 -->
        <div class="demo-card">
          <h2 class="demo-title">🌐 API 测试结果</h2>
          <div class="text-sm text-gray-300">
            <div v-if="apiResult">
              <div class="flex items-center mb-2">
                <span class="mr-2">{{ apiResult.success ? '✅' : '❌' }}</span>
                状态: {{ apiResult.success ? '成功' : '失败' }}
              </div>
              <div v-if="apiResult.error" class="text-red-400 mb-2">
                <span class="mr-2">❌</span>
                错误: {{ apiResult.error }}
              </div>
              <div v-if="apiResult.data" class="text-xs">
                <span class="mr-2">📄</span>
                数据: <code class="text-gray-400">{{ JSON.stringify(apiResult.data).substring(0, 200) }}...</code>
              </div>
            </div>
            <div v-else class="text-gray-500 text-center py-4">
              <div class="text-2xl mb-2">📋</div>
              暂无测试结果
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import LiveOpenings from '~/components/home/<USER>'

const socketStore = useSocketStore()
const apiResult = ref<any>(null)

// 定时器引用，用于清理
let initTimer: NodeJS.Timeout | null = null

// 生成模拟开箱记录
function generateMockRecord(id: number) {
  const rarities = [
    { name: 'common', color: '#b0c3d9', color_hex: '#b0c3d9' },
    { name: 'uncommon', color: '#5e98d9', color_hex: '#5e98d9' },
    { name: 'rare', color: '#4b69ff', color_hex: '#4b69ff' },
    { name: 'mythical', color: '#8847ff', color_hex: '#8847ff' },
    { name: 'legendary', color: '#d32ce6', color_hex: '#d32ce6' },
    { name: 'ancient', color: '#eb4b4b', color_hex: '#eb4b4b' }
  ]

  const selectedRarity = rarities[Math.floor(Math.random() * rarities.length)]

  const weapons = [
    'AK-47 | 红线',
    'AWP | 龙狙',
    'M4A4 | 咆哮',
    'Glock-18 | 水元素',
    'USP-S | 杀戮确认',
    'Desert Eagle | 印花集锦'
  ]

  const exteriors = ['崭新出厂', '略有磨损', '久经沙场', '破损不堪', '战痕累累']

  const selectedWeapon = weapons[Math.floor(Math.random() * weapons.length)]
  const selectedExterior = exteriors[Math.floor(Math.random() * exteriors.length)]

  return {
    id: id,
    uid: `record_${id}_${Date.now()}`,
    create_time: new Date().toISOString(),
    update_time: new Date().toISOString(),
    price: Math.floor(Math.random() * 500) + 50,
    source: 1,
    user: Math.floor(Math.random() * 1000),
    case: Math.floor(Math.random() * 10) + 1,
    open_count: Math.floor(Math.random() * 100) + 1,
    user_info: {
      profile: {
        avatar: '/images/avatars/default.jpg',
        nickname: `用户${Math.floor(Math.random() * 1000)}`
      },
      uid: `user_${Math.floor(Math.random() * 1000)}`
    },
    case_info: {
      id: Math.floor(Math.random() * 10) + 1,
      cover: '/images/cases/case1.jpg',
      name: `测试箱子 ${Math.floor(Math.random() * 5) + 1}`,
      name_en: `Test Case ${Math.floor(Math.random() * 5) + 1}`,
      name_zh_hans: `测试箱子 ${Math.floor(Math.random() * 5) + 1}`,
      case_key: `test_case_${Math.floor(Math.random() * 5) + 1}`,
      price: Math.floor(Math.random() * 50) + 10,
      open_count: Math.floor(Math.random() * 1000) + 100
    },
    item_info: {
      id: Math.floor(Math.random() * 100) + 1,
      item_price: {
        update_time: new Date().toISOString(),
        price: Math.floor(Math.random() * 500) + 50
      },
      item_category: {
        cate_id: 1,
        cate_name: '武器',
        cate_name_en: 'Weapon',
        cate_name_zh_hans: '武器',
        icon: null
      },
      item_quality: {
        quality_id: 1,
        quality_name: '普通',
        quality_name_en: 'Normal',
        quality_name_zh_hans: '普通',
        quality_color: '#ffffff'
      },
      item_rarity: {
        rarity_id: selectedRarity.name === 'legendary' ? 5 : 3,
        rarity_name: selectedRarity.name,
        rarity_name_en: selectedRarity.name,
        rarity_name_zh_hans: selectedRarity.name === 'legendary' ? '传说' : '稀有',
        rarity_color: selectedRarity.color
      },
      item_exterior: {
        exterior_id: 1,
        exterior_name: selectedExterior,
        exterior_name_en: selectedExterior,
        exterior_name_zh_hans: selectedExterior,
        exterior_color: '#cccccc'
      },
      image: '/images/skins/ak47_redline.jpg',
      name: `${selectedWeapon} (${selectedExterior})`,
      name_en: `${selectedWeapon} (${selectedExterior})`,
      name_zh_hans: `${selectedWeapon} (${selectedExterior})`
    }
  }
}

// 加载模拟数据
function loadMockData() {
  const mockRecords = []
  for (let i = 1; i <= 10; i++) {
    mockRecords.push(generateMockRecord(i))
  }
  
  console.log('[LiveOpenings测试] 加载模拟数据:', mockRecords)
  socketStore.setCaseRecords(mockRecords)
}

// 添加新记录
function addNewRecord() {
  const newRecord = generateMockRecord(Date.now())
  const currentRecords = [...socketStore.caseRecords]
  currentRecords.unshift(newRecord)
  
  console.log('[LiveOpenings测试] 添加新记录:', newRecord)
  socketStore.setCaseRecords(currentRecords)
}

// 清空数据
function clearData() {
  console.log('[LiveOpenings测试] 清空数据')
  socketStore.setCaseRecords([])
}

// 测试真实 API
async function testRealApi() {
  try {
    console.log('[LiveOpenings测试] 测试真实 API...')
    const response = await $fetch('/api/monitor/data/') as any

    apiResult.value = {
      success: true,
      data: response
    }

    // 如果 API 返回了数据，设置到 store
    if (response && response.code === 0 && response.body?.case_records) {
      console.log('[LiveOpenings测试] API 返回数据，设置到 store')
      socketStore.setCaseRecords(response.body.case_records)
    }

  } catch (error: any) {
    console.error('[LiveOpenings测试] API 测试失败:', error)
    apiResult.value = {
      success: false,
      error: error.message
    }
  }
}

// 页面加载时自动加载模拟数据
onMounted(() => {
  initTimer = setTimeout(() => {
    loadMockData()
  }, 1000)
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (initTimer) {
    clearTimeout(initTimer)
    initTimer = null
  }
})
</script>
