<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 p-6">
    <div class="max-w-6xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-4">🌐 API 接口测试</h1>
        <p class="text-gray-300 text-lg">测试各个 API 端点的响应状态和数据格式，验证代理配置</p>
        <div class="mt-4">
          <NuxtLink to="/demo" class="nav-button">
            <i class="fas fa-arrow-left mr-2"></i>
            返回演示中心
          </NuxtLink>
        </div>
      </div>

      <div class="space-y-6">
        <!-- API 测试结果 -->
        <div class="demo-card">
          <h2 class="demo-title">📊 API 测试结果</h2>
          <div class="space-y-3">
            <div v-for="(result, index) in apiResults" :key="index" class="border border-gray-600 rounded p-3 bg-gray-800">
              <div class="flex items-center space-x-2 mb-2">
                <span :class="result.success ? 'text-green-400' : 'text-red-400'">
                  {{ result.success ? '✅' : '❌' }}
                </span>
                <span class="font-mono text-sm text-primary">{{ result.url }}</span>
              </div>
              <div class="text-sm text-gray-300 ml-6 flex items-center">
                <span class="mr-2">⏱️</span>
                状态: {{ result.status }} | 响应时间: <span class="text-primary">{{ result.time }}ms</span>
              </div>
              <div v-if="result.error" class="text-sm text-red-400 ml-6 flex items-center">
                <span class="mr-2">❌</span>
                错误: {{ result.error }}
              </div>
              <div v-if="result.data" class="text-sm text-gray-300 ml-6 mt-2">
                <div class="flex items-center mb-1">
                  <span class="mr-2">📄</span>
                  数据:
                </div>
                <pre class="text-xs text-gray-400 bg-gray-900 p-2 rounded overflow-x-auto">{{ JSON.stringify(result.data, null, 2).substring(0, 200) }}...</pre>
              </div>
            </div>
            <div v-if="apiResults.length === 0" class="text-gray-500 text-center py-8">
              <div class="text-4xl mb-2">📭</div>
              暂无测试结果
            </div>
          </div>
        </div>

        <!-- 控制面板 -->
        <div class="demo-card">
          <h2 class="demo-title">🎮 测试控制</h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <button @click="testAllApis" class="demo-button demo-button-primary">
              🌐 测试所有 API
            </button>
            <button @click="testMonitorApi" class="demo-button demo-button-success">
              📊 测试监控 API
            </button>
            <button @click="testBannerApi" class="demo-button demo-button-purple">
              🎨 测试 Banner API
            </button>
            <button @click="clearResults" class="demo-button demo-button-secondary">
              🗑️ 清空结果
            </button>
          </div>
        </div>

        <!-- Socket Store 状态 -->
        <div class="demo-card">
          <h2 class="demo-title">📊 Socket Store 状态</h2>
          <div class="text-sm space-y-2 text-gray-300">
            <div class="flex items-center">
              <span class="mr-2">{{ socketStore.isConnected ? '✅' : '❌' }}</span>
              连接状态: {{ socketStore.isConnected ? '已连接' : '未连接' }}
            </div>
            <div class="flex items-center">
              <span class="mr-2">🆔</span>
              Socket ID: <code class="text-primary">{{ socketStore.socketId || '无' }}</code>
            </div>
            <div class="flex items-center">
              <span class="mr-2">📦</span>
              开箱记录数量: <span class="text-primary">{{ socketStore.caseRecords.length }}</span>
            </div>
            <div class="text-xs">
              <span class="mr-2">📊</span>
              统计数据: <code class="text-gray-400">{{ JSON.stringify(socketStore.statsData) }}</code>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const socketStore = useSocketStore()
const apiResults = ref<any[]>([])

// 定时器引用，用于清理
let initTimer: NodeJS.Timeout | null = null

interface ApiTestResult {
  url: string
  success: boolean
  status: string
  time: number
  data?: any
  error?: string
}

async function testApi(url: string): Promise<ApiTestResult> {
  const startTime = Date.now()
  
  try {
    const response = await $fetch(url)
    const endTime = Date.now()
    
    return {
      url,
      success: true,
      status: 'OK',
      time: endTime - startTime,
      data: response
    }
  } catch (error: any) {
    const endTime = Date.now()
    
    return {
      url,
      success: false,
      status: 'ERROR',
      time: endTime - startTime,
      error: error.message || String(error)
    }
  }
}

async function testMonitorApi() {
  const result = await testApi('/api/monitor/data/')
  apiResults.value.unshift(result)
}

async function testBannerApi() {
  const result = await testApi('/api/sitecfg/banner/')
  apiResults.value.unshift(result)
}

async function testAllApis() {
  const apis = [
    '/api/monitor/data/',
    '/api/sitecfg/banner/',
    '/api/box/tag/?q=HOT&num=5',
    '/api/box/tag/?q=new&num=5',
    '/api/package/items/random?num=12'
  ]
  
  for (const api of apis) {
    const result = await testApi(api)
    apiResults.value.unshift(result)
  }
}

function clearResults() {
  apiResults.value = []
}

// 页面加载时自动测试
onMounted(() => {
  initTimer = setTimeout(() => {
    testMonitorApi()
  }, 1000)
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (initTimer) {
    clearTimeout(initTimer)
    initTimer = null
  }
})
</script>
