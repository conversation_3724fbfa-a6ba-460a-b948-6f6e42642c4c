from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from django.db import transaction
from box.models import <PERSON>Room, CaseRoomRound, CaseRoomBet, GameState, RoomType
from box.business_room import ws_send_room_data, ws_send_box_room
from box.serializers import CaseRoomSerializer
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '清理卡住的对战房间'

    def add_arguments(self, parser):
        parser.add_argument(
            '--timeout-minutes',
            type=int,
            default=30,
            help='超过多少分钟的房间被认为是卡住的 (默认: 30分钟)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示会被清理的房间，不实际执行清理'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制清理所有卡住的房间，包括刚创建的'
        )
        parser.add_argument(
            '--stats-only',
            action='store_true',
            help='只显示统计信息，不执行清理'
        )
        parser.add_argument(
            '--cleanup-orphaned',
            action='store_true',
            help='清理孤立的轮次数据'
        )
        parser.add_argument(
            '--cleanup-old-rooms',
            type=int,
            metavar='DAYS',
            help='清理指定天数前的已结束房间'
        )

    def handle(self, *args, **options):
        timeout_minutes = options['timeout_minutes']
        dry_run = options['dry_run']
        force = options['force']
        stats_only = options['stats_only']
        cleanup_orphaned = options['cleanup_orphaned']
        cleanup_old_rooms = options['cleanup_old_rooms']

        # 显示统计信息
        self.show_statistics()

        if stats_only:
            return

        # 清理孤立数据
        if cleanup_orphaned:
            self.cleanup_orphaned_rounds()

        # 清理旧房间
        if cleanup_old_rooms:
            self.cleanup_old_ended_rooms(cleanup_old_rooms)

        # 如果只是清理孤立数据或旧房间，不继续处理卡住的房间
        if cleanup_orphaned or cleanup_old_rooms:
            if not any([not stats_only, not cleanup_orphaned, not cleanup_old_rooms]):
                return

        self.stdout.write(f'\n=== 开始清理超过 {timeout_minutes} 分钟的卡住对战房间 ===')
        
        # 计算超时时间点
        timeout_time = timezone.now() - timedelta(minutes=timeout_minutes)
        
        # 查找卡住的房间
        stuck_rooms_query = CaseRoom.objects.filter(
            state__in=[GameState.Full.value, GameState.Running.value],
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=timeout_time
        )
        
        if not force:
            # 额外检查：确保房间至少存在了一定时间
            min_create_time = timezone.now() - timedelta(minutes=max(timeout_minutes, 10))
            stuck_rooms_query = stuck_rooms_query.filter(create_time__lt=min_create_time)
        
        stuck_rooms = stuck_rooms_query.order_by('update_time')
        
        self.stdout.write(f'发现 {stuck_rooms.count()} 个卡住的对战房间')
        
        if stuck_rooms.count() == 0:
            self.stdout.write(self.style.SUCCESS('没有发现卡住的对战房间'))
            return
        
        # 显示房间信息
        for room in stuck_rooms[:10]:  # 只显示前10个
            self.stdout.write(
                f'房间 {room.short_id}: 状态={room.state}, 类型={room.type}, '
                f'更新时间={room.update_time}, 创建时间={room.create_time}'
            )
        
        if stuck_rooms.count() > 10:
            self.stdout.write(f'... 还有 {stuck_rooms.count() - 10} 个房间')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('这是试运行模式，不会实际清理房间'))
            return
        
        # 确认清理
        if not force:
            confirm = input(f'确定要清理这 {stuck_rooms.count()} 个房间吗? (y/N): ')
            if confirm.lower() != 'y':
                self.stdout.write('取消清理操作')
                return
        
        # 开始清理
        cleaned_count = 0
        error_count = 0
        
        for room in stuck_rooms:
            try:
                self.cleanup_room(room)
                cleaned_count += 1
                self.stdout.write(f'✓ 清理房间 {room.short_id}')
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'✗ 清理房间 {room.short_id} 失败: {str(e)}')
                )
                logger.exception(f'清理房间失败: {room.short_id}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'清理完成: 成功 {cleaned_count} 个, 失败 {error_count} 个'
            )
        )

    def cleanup_room(self, room):
        """清理单个房间"""
        with transaction.atomic():
            # 重新获取房间以确保数据最新
            room = CaseRoom.objects.select_for_update().get(uid=room.uid)
            
            # 检查房间是否还需要清理
            if room.state not in [GameState.Full.value, GameState.Running.value]:
                return
            
            # 获取房间的参与者
            bets = CaseRoomBet.objects.filter(room=room)
            
            if bets.exists():
                # 如果有参与者，尝试正常结束游戏
                self.force_end_battle(room, bets)
            else:
                # 如果没有参与者，直接取消房间
                room.state = GameState.Cancelled.value
                room.save()
            
            # 发送房间更新通知
            try:
                room_data = CaseRoomSerializer(room).data
                ws_send_box_room(room_data, 'update')
                if room.state == GameState.End.value:
                    ws_send_room_data(room, 'end')
            except Exception as e:
                logger.warning(f'发送房间更新通知失败: {room.short_id}, error: {e}')

    def force_end_battle(self, room, bets):
        """强制结束对战"""
        from django.db.models import Sum
        import random
        
        # 计算总金额和平均金额
        bets_amount_qs = bets.aggregate(Sum('open_amount'))
        bets_amount = bets_amount_qs.get('open_amount__sum', 0) or 0
        
        if len(bets) == 0:
            room.state = GameState.Cancelled.value
            room.save()
            return
        
        # 按开箱金额排序确定获胜者
        sorted_bets = bets.order_by('-open_amount')
        winner = sorted_bets[0]
        
        # 如果有平局，随机选择获胜者
        if len(sorted_bets) >= 2 and sorted_bets[0].open_amount == sorted_bets[1].open_amount:
            tied_bets = [bet for bet in sorted_bets if bet.open_amount == sorted_bets[0].open_amount]
            winner = random.choice(tied_bets)
        
        # 设置获胜者
        winner.victory = 1
        if winner.win_amount is None:
            winner.win_amount = 0
        if winner.win_items_count is None:
            winner.win_items_count = 0
        
        # 简单的奖励分配 - 获胜者获得所有金额
        winner.win_amount += bets_amount
        winner.save()
        
        # 更新房间状态
        room.state = GameState.End.value
        room.save()
        
        logger.info(f'强制结束对战房间 {room.short_id}, 获胜者: {winner.user.username}')

    def show_statistics(self):
        """显示对战房间统计信息"""
        from django.db.models import Count

        self.stdout.write('\n=== 对战房间统计信息 ===')

        # 统计各状态房间数量
        stats = CaseRoom.objects.filter(
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
        ).values('state').annotate(count=Count('id')).order_by('state')

        state_names = {
            1: 'Initial',
            2: 'Joinable',
            3: 'Joining',
            4: 'Full',
            5: 'Running',
            11: 'End',
            20: 'Cancelled'
        }

        total_rooms = 0
        for stat in stats:
            state_name = state_names.get(stat['state'], f'Unknown({stat["state"]})')
            count = stat['count']
            total_rooms += count
            self.stdout.write(f'状态 {stat["state"]} ({state_name}): {count} 个房间')

        self.stdout.write(f'总计: {total_rooms} 个对战房间')

        # 统计长时间运行的房间
        timeout_time = timezone.now() - timedelta(minutes=30)
        long_running = CaseRoom.objects.filter(
            state__in=[GameState.Full.value, GameState.Running.value],
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=timeout_time
        ).count()

        if long_running > 0:
            self.stdout.write(self.style.WARNING(f'发现 {long_running} 个可能卡住的房间 (超过30分钟)'))
        else:
            self.stdout.write(self.style.SUCCESS('没有发现卡住的房间'))

    def cleanup_orphaned_rounds(self):
        """清理孤立的轮次数据"""
        self.stdout.write('\n=== 清理孤立的轮次数据 ===')

        # 查找没有对应房间的轮次
        orphaned_rounds = CaseRoomRound.objects.filter(
            room__isnull=True
        ) | CaseRoomRound.objects.filter(
            room__state=GameState.Cancelled.value,
            room__update_time__lt=timezone.now() - timedelta(days=1)
        )

        count = orphaned_rounds.count()
        if count > 0:
            orphaned_rounds.delete()
            self.stdout.write(f'清理了 {count} 个孤立的轮次记录')
        else:
            self.stdout.write('没有发现孤立的轮次记录')

    def cleanup_old_ended_rooms(self, days=7):
        """清理旧的已结束房间"""
        self.stdout.write(f'\n=== 清理 {days} 天前的已结束房间 ===')

        old_time = timezone.now() - timedelta(days=days)
        old_rooms = CaseRoom.objects.filter(
            state__in=[GameState.End.value, GameState.Cancelled.value],
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=old_time
        )

        count = old_rooms.count()
        if count > 0:
            # 先清理相关的轮次和投注记录
            for room in old_rooms:
                CaseRoomRound.objects.filter(room=room).delete()
                CaseRoomBet.objects.filter(room=room).delete()

            old_rooms.delete()
            self.stdout.write(f'清理了 {count} 个旧的已结束房间')
        else:
            self.stdout.write('没有发现需要清理的旧房间')
