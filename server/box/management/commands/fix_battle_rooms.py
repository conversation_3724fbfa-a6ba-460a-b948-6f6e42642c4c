#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Django管理命令：修复对战房间状态
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from datetime import timedelta

from box.models import CaseRoom, CaseRoomBet, GameState, RoomType


class Command(BaseCommand):
    help = '修复对战房间状态问题'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示需要修复的房间，不实际修复',
        )
        parser.add_argument(
            '--minutes',
            type=int,
            default=3,
            help='卡住时间阈值（分钟），默认3分钟',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        minutes_threshold = options['minutes']
        
        self.stdout.write(
            self.style.SUCCESS(f'开始检查对战房间状态（阈值：{minutes_threshold}分钟）...')
        )
        
        if dry_run:
            self.stdout.write(self.style.WARNING('*** 这是预览模式，不会实际修改数据 ***'))
        
        # 1. 修复卡住的满员房间
        self.fix_stuck_full_rooms(dry_run, minutes_threshold)
        
        # 2. 修复长时间运行的房间
        self.fix_long_running_rooms(dry_run, minutes_threshold * 10)  # 运行房间用更长的阈值
        
        # 3. 显示当前状态
        self.show_current_status()
        
        self.stdout.write(self.style.SUCCESS('修复完成！'))

    def fix_stuck_full_rooms(self, dry_run, minutes_threshold):
        """修复卡住的满员房间"""
        
        threshold_time = timezone.now() - timedelta(minutes=minutes_threshold)
        stuck_rooms = CaseRoom.objects.filter(
            state=GameState.Full.value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=threshold_time
        )
        
        self.stdout.write(f'🔍 发现 {stuck_rooms.count()} 个卡住的满员房间')
        
        if stuck_rooms.count() == 0:
            return
        
        fixed_count = 0
        for room in stuck_rooms:
            bets_count = room.bets.count()
            
            self.stdout.write(f'  房间 {room.short_id}: {bets_count}/{room.max_joiner} 参与者')
            
            if not dry_run:
                try:
                    with transaction.atomic():
                        # 重新获取房间以确保数据最新
                        room = CaseRoom.objects.select_for_update().get(id=room.id)
                        bets_count = room.bets.count()
                        
                        if bets_count == room.max_joiner:
                            # 满员房间，设置为运行状态
                            room.state = GameState.Running.value
                            room.save()
                            self.stdout.write(
                                self.style.SUCCESS(f'    ✅ 设置为运行状态')
                            )
                            fixed_count += 1
                        elif bets_count > 0:
                            # 有参与者但未满员，重置为可加入状态
                            room.state = GameState.Joinable.value
                            room.save()
                            self.stdout.write(
                                self.style.WARNING(f'    🔄 重置为可加入状态')
                            )
                            fixed_count += 1
                        else:
                            # 无参与者，取消房间
                            room.state = GameState.Cancelled.value
                            room.save()
                            self.stdout.write(
                                self.style.ERROR(f'    ❌ 取消房间（无参与者）')
                            )
                            fixed_count += 1
                            
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'    ❌ 修复失败: {e}')
                    )
            else:
                # 预览模式
                if bets_count == room.max_joiner:
                    self.stdout.write(f'    → 将设置为运行状态')
                elif bets_count > 0:
                    self.stdout.write(f'    → 将重置为可加入状态')
                else:
                    self.stdout.write(f'    → 将取消房间（无参与者）')
        
        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'✅ 修复满员房间: {fixed_count} 个')
            )

    def fix_long_running_rooms(self, dry_run, minutes_threshold):
        """修复长时间运行的房间"""
        
        threshold_time = timezone.now() - timedelta(minutes=minutes_threshold)
        long_running_rooms = CaseRoom.objects.filter(
            state=GameState.Running.value,
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__lt=threshold_time
        )
        
        self.stdout.write(f'🔍 发现 {long_running_rooms.count()} 个长时间运行的房间')
        
        if long_running_rooms.count() == 0:
            return
        
        fixed_count = 0
        for room in long_running_rooms:
            bets_count = room.bets.count()
            time_diff = timezone.now() - room.update_time
            
            self.stdout.write(
                f'  房间 {room.short_id}: {bets_count}/{room.max_joiner} 参与者, '
                f'运行时间: {time_diff.total_seconds()/60:.1f}分钟'
            )
            
            if not dry_run:
                try:
                    with transaction.atomic():
                        room = CaseRoom.objects.select_for_update().get(id=room.id)
                        
                        if bets_count > 0:
                            # 有参与者，设置为结束状态
                            room.state = GameState.End.value
                            room.save()
                            self.stdout.write(
                                self.style.SUCCESS(f'    ✅ 设置为结束状态')
                            )
                            fixed_count += 1
                        else:
                            # 无参与者，取消房间
                            room.state = GameState.Cancelled.value
                            room.save()
                            self.stdout.write(
                                self.style.ERROR(f'    ❌ 取消房间（无参与者）')
                            )
                            fixed_count += 1
                            
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'    ❌ 修复失败: {e}')
                    )
            else:
                # 预览模式
                if bets_count > 0:
                    self.stdout.write(f'    → 将设置为结束状态')
                else:
                    self.stdout.write(f'    → 将取消房间（无参与者）')
        
        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'✅ 修复运行房间: {fixed_count} 个')
            )

    def show_current_status(self):
        """显示当前房间状态"""
        
        self.stdout.write('\n📊 当前房间状态统计:')
        
        states = [
            (GameState.Initial.value, "初始"),
            (GameState.Joinable.value, "可加入"),
            (GameState.Joining.value, "加入中"),
            (GameState.Full.value, "满员"),
            (GameState.Running.value, "进行中"),
            (GameState.End.value, "已结束"),
            (GameState.Cancelled.value, "已取消")
        ]
        
        total_rooms = 0
        for state_value, state_name in states:
            count = CaseRoom.objects.filter(
                state=state_value,
                type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
            ).count()
            if count > 0:
                self.stdout.write(f'  {state_name}: {count} 个')
                total_rooms += count
        
        self.stdout.write(f'  总计: {total_rooms} 个对战房间')
        
        # 显示最近活动的房间
        recent_rooms = CaseRoom.objects.filter(
            type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
            update_time__gte=timezone.now() - timedelta(minutes=10)
        ).order_by('-update_time')[:5]
        
        if recent_rooms.exists():
            self.stdout.write('\n🕐 最近10分钟活动的房间:')
            for room in recent_rooms:
                state_name = dict(states).get(room.state, f"未知({room.state})")
                bets_count = room.bets.count()
                time_diff = timezone.now() - room.update_time
                self.stdout.write(
                    f'  {room.short_id}: {state_name} ({bets_count}/{room.max_joiner}) - '
                    f'{time_diff.total_seconds():.0f}秒前'
                )
