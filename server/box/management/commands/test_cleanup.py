from django.core.management.base import BaseCommand
from box.models import CaseRoom, GameState, RoomType
from django.db.models import Count


class Command(BaseCommand):
    help = '测试清理命令'

    def handle(self, *args, **options):
        self.stdout.write('开始测试清理命令...')
        
        try:
            # 统计各状态房间数量
            stats = CaseRoom.objects.filter(
                type__in=[RoomType.Battle.value, RoomType.TeamBattle.value]
            ).values('state').annotate(count=Count('id')).order_by('state')
            
            state_names = {
                1: 'Initial',
                2: 'Joinable', 
                3: 'Joining',
                4: 'Full',
                5: 'Running',
                11: 'End',
                20: 'Cancelled'
            }
            
            self.stdout.write('=== 对战房间统计信息 ===')
            total_rooms = 0
            for stat in stats:
                state_name = state_names.get(stat['state'], f'Unknown({stat["state"]})')
                count = stat['count']
                total_rooms += count
                self.stdout.write(f'状态 {stat["state"]} ({state_name}): {count} 个房间')
            
            self.stdout.write(f'总计: {total_rooms} 个对战房间')
            
            # 检查可能卡住的房间
            from django.utils import timezone
            from datetime import timedelta
            
            timeout_time = timezone.now() - timedelta(minutes=30)
            stuck_rooms = CaseRoom.objects.filter(
                state__in=[GameState.Full.value, GameState.Running.value],
                type__in=[RoomType.Battle.value, RoomType.TeamBattle.value],
                update_time__lt=timeout_time
            )
            
            self.stdout.write(f'\n可能卡住的房间 (超过30分钟): {stuck_rooms.count()} 个')
            
            if stuck_rooms.count() > 0:
                self.stdout.write("卡住的房间详情:")
                for room in stuck_rooms[:5]:  # 只显示前5个
                    self.stdout.write(f'  房间 {room.short_id}: 状态={room.state}, 更新时间={room.update_time}')
            
            self.stdout.write(self.style.SUCCESS('✓ 测试完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ 测试失败: {e}'))
            import traceback
            traceback.print_exc()
