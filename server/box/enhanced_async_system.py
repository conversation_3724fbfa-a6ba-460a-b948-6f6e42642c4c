#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的异步处理系统
提供更稳定、更高效的对战房间异步处理能力
"""

import asyncio
import logging
import time
import threading
import queue
from typing import Optional, Dict, Any, List, Callable
from concurrent.futures import ThreadPoolExecutor, Future
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class AsyncTask:
    """异步任务数据类"""
    task_id: str
    room_uid: str
    task_type: str
    payload: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    created_at: float = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()


class EnhancedAsyncProcessor:
    """增强的异步处理器"""
    
    def __init__(self, max_workers: int = 4, max_queue_size: int = 1000):
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        
        # 任务队列
        self.task_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self.active_tasks: Dict[str, AsyncTask] = {}
        self.completed_tasks: Dict[str, AsyncTask] = {}
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 控制标志
        self.is_running = False
        self.worker_threads: List[threading.Thread] = []
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'active_tasks': 0,
            'queue_size': 0
        }
        
        # 任务处理器注册表
        self.task_handlers: Dict[str, Callable] = {}
        
        logger.info(f"增强异步处理器初始化完成: max_workers={max_workers}")
    
    def register_handler(self, task_type: str, handler: Callable):
        """注册任务处理器"""
        self.task_handlers[task_type] = handler
        logger.info(f"注册任务处理器: {task_type}")
    
    def start(self):
        """启动异步处理器"""
        if self.is_running:
            logger.warning("异步处理器已在运行")
            return
        
        self.is_running = True
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"AsyncWorker-{i}",
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)
        
        # 启动监控线程
        monitor = threading.Thread(
            target=self._monitor_loop,
            name="AsyncMonitor",
            daemon=True
        )
        monitor.start()
        
        logger.info(f"异步处理器已启动: {len(self.worker_threads)} 个工作线程")
    
    def stop(self):
        """停止异步处理器"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 等待工作线程结束
        for worker in self.worker_threads:
            worker.join(timeout=5)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        logger.info("异步处理器已停止")
    
    def submit_task(self, task: AsyncTask, priority: int = 5) -> bool:
        """提交任务到队列"""
        try:
            if self.task_queue.qsize() >= self.max_queue_size:
                logger.warning(f"任务队列已满，拒绝任务: {task.task_id}")
                return False
            
            # 优先级越小越高
            self.task_queue.put((priority, time.time(), task), timeout=1)
            self.stats['total_tasks'] += 1
            self.stats['queue_size'] = self.task_queue.qsize()
            
            logger.debug(f"任务已提交: {task.task_id} (优先级: {priority})")
            return True
            
        except queue.Full:
            logger.error(f"任务队列已满，无法提交任务: {task.task_id}")
            return False
    
    def _worker_loop(self):
        """工作线程主循环"""
        worker_name = threading.current_thread().name
        logger.info(f"工作线程启动: {worker_name}")
        
        while self.is_running:
            try:
                # 获取任务（阻塞1秒）
                try:
                    priority, timestamp, task = self.task_queue.get(timeout=1)
                    self.stats['queue_size'] = self.task_queue.qsize()
                except queue.Empty:
                    continue
                
                # 处理任务
                self._process_task(task, worker_name)
                
            except Exception as e:
                logger.error(f"工作线程 {worker_name} 发生错误: {e}")
        
        logger.info(f"工作线程停止: {worker_name}")
    
    def _process_task(self, task: AsyncTask, worker_name: str):
        """处理单个任务"""
        task.status = TaskStatus.RUNNING
        task.started_at = time.time()
        self.active_tasks[task.task_id] = task
        self.stats['active_tasks'] = len(self.active_tasks)
        
        logger.info(f"[{worker_name}] 开始处理任务: {task.task_id} ({task.task_type})")
        
        try:
            # 查找任务处理器
            handler = self.task_handlers.get(task.task_type)
            if not handler:
                raise ValueError(f"未找到任务处理器: {task.task_type}")
            
            # 执行任务
            result = handler(task.payload)
            
            # 任务完成
            task.status = TaskStatus.COMPLETED
            task.completed_at = time.time()
            
            # 移动到完成列表
            self.active_tasks.pop(task.task_id, None)
            self.completed_tasks[task.task_id] = task
            
            # 清理旧的完成任务（保留最近1000个）
            if len(self.completed_tasks) > 1000:
                oldest_tasks = sorted(
                    self.completed_tasks.items(),
                    key=lambda x: x[1].completed_at or 0
                )[:100]
                for task_id, _ in oldest_tasks:
                    self.completed_tasks.pop(task_id, None)
            
            self.stats['completed_tasks'] += 1
            self.stats['active_tasks'] = len(self.active_tasks)
            
            duration = task.completed_at - task.started_at
            logger.info(f"[{worker_name}] 任务完成: {task.task_id} (耗时: {duration:.3f}s)")
            
        except Exception as e:
            # 任务失败
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = time.time()
            
            # 检查是否需要重试
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.PENDING
                
                # 重新提交任务（降低优先级）
                retry_priority = 8  # 重试任务优先级较低
                self.submit_task(task, retry_priority)
                
                logger.warning(f"[{worker_name}] 任务失败，准备重试 ({task.retry_count}/{task.max_retries}): {task.task_id}")
            else:
                # 移动到完成列表
                self.active_tasks.pop(task.task_id, None)
                self.completed_tasks[task.task_id] = task
                self.stats['failed_tasks'] += 1
                
                logger.error(f"[{worker_name}] 任务最终失败: {task.task_id}, 错误: {e}")
            
            self.stats['active_tasks'] = len(self.active_tasks)
    
    def _monitor_loop(self):
        """监控线程主循环"""
        logger.info("监控线程启动")
        
        last_stats_time = time.time()
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # 每30秒输出统计信息
                if current_time - last_stats_time >= 30:
                    self._log_stats()
                    last_stats_time = current_time
                
                # 检查超时任务
                self._check_timeout_tasks()
                
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"监控线程发生错误: {e}")
        
        logger.info("监控线程停止")
    
    def _log_stats(self):
        """输出统计信息"""
        logger.info(f"异步处理器统计: {json.dumps(self.stats, indent=2)}")
    
    def _check_timeout_tasks(self):
        """检查超时任务"""
        current_time = time.time()
        timeout_threshold = 300  # 5分钟超时
        
        timeout_tasks = []
        for task_id, task in self.active_tasks.items():
            if task.started_at and (current_time - task.started_at) > timeout_threshold:
                timeout_tasks.append(task)
        
        for task in timeout_tasks:
            logger.warning(f"任务超时，标记为失败: {task.task_id}")
            task.status = TaskStatus.FAILED
            task.error_message = "任务执行超时"
            task.completed_at = current_time
            
            self.active_tasks.pop(task.task_id, None)
            self.completed_tasks[task.task_id] = task
            self.stats['failed_tasks'] += 1
            self.stats['active_tasks'] = len(self.active_tasks)
    
    def get_task_status(self, task_id: str) -> Optional[AsyncTask]:
        """获取任务状态"""
        # 检查活跃任务
        if task_id in self.active_tasks:
            return self.active_tasks[task_id]
        
        # 检查完成任务
        if task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'queue_size': self.task_queue.qsize(),
            'active_tasks': len(self.active_tasks),
            'is_running': self.is_running
        }


# 对战房间专用处理器
class BattleRoomAsyncProcessor:
    """对战房间专用异步处理器"""

    def __init__(self):
        self.processor = EnhancedAsyncProcessor(max_workers=6, max_queue_size=2000)
        self._register_battle_handlers()
        self.processor.start()
        logger.info("对战房间异步处理器已启动")

    def _register_battle_handlers(self):
        """注册对战相关的任务处理器"""

        def handle_round_completion(payload: Dict[str, Any]) -> Dict[str, Any]:
            """处理轮次完成 - 实际推进下一轮或结束对战"""
            try:
                room_uid = payload.get('room_uid')
                current_round = payload.get('current_round')
                total_rounds = payload.get('total_rounds')

                logger.info(f"异步处理轮次完成: room={room_uid}, round={current_round}/{total_rounds}")

                # 🔥 关键修复：实际的轮次推进逻辑
                # 延迟一小段时间，让前端动画播放完成
                time.sleep(2)  # 2秒延迟，让动画完成

                # 🔥 修复：确保Django环境已正确设置
                try:
                    from box.business_room import run_battle_room
                except ImportError as import_error:
                    logger.error(f"无法导入Django模块: {import_error}")
                    # 尝试设置Django环境
                    import os
                    import django
                    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'steambase.settings')
                    django.setup()
                    from box.business_room import run_battle_room

                # 🔥 关键：调用实际的对战推进逻辑
                logger.info(f"异步推进下一轮: room={room_uid}, 当前轮次={current_round}")
                run_battle_room(room_uid)

                return {
                    'status': 'success',
                    'room_uid': room_uid,
                    'round': current_round,
                    'total_rounds': total_rounds,
                    'processed_at': time.time(),
                    'action': 'round_progressed'
                }

            except Exception as e:
                logger.error(f"轮次完成处理失败: room={payload.get('room_uid')}, error={e}")
                raise

        def handle_battle_start(payload: Dict[str, Any]) -> Dict[str, Any]:
            """处理对战开始"""
            try:
                room_uid = payload.get('room_uid')
                logger.info(f"异步处理对战开始: room={room_uid}")

                return {
                    'status': 'success',
                    'room_uid': room_uid,
                    'started_at': time.time()
                }

            except Exception as e:
                logger.error(f"对战开始处理失败: {e}")
                raise

        def handle_battle_end(payload: Dict[str, Any]) -> Dict[str, Any]:
            """处理对战结束"""
            try:
                room_uid = payload.get('room_uid')
                logger.info(f"异步处理对战结束: room={room_uid}")

                return {
                    'status': 'success',
                    'room_uid': room_uid,
                    'ended_at': time.time()
                }

            except Exception as e:
                logger.error(f"对战结束处理失败: {e}")
                raise

        # 注册处理器
        self.processor.register_handler('round_completion', handle_round_completion)
        self.processor.register_handler('battle_start', handle_battle_start)
        self.processor.register_handler('battle_end', handle_battle_end)

    def submit_round_completion(self, room_uid: str, round_data: Dict[str, Any], priority: int = 3) -> str:
        """提交轮次完成任务"""
        task_id = f"round_{room_uid}_{round_data.get('current_round')}_{int(time.time() * 1000)}"

        task = AsyncTask(
            task_id=task_id,
            room_uid=room_uid,
            task_type='round_completion',
            payload=round_data
        )

        if self.processor.submit_task(task, priority):
            logger.debug(f"轮次完成任务已提交: {task_id}")
            return task_id
        else:
            logger.error(f"轮次完成任务提交失败: {task_id}")
            return None

    def submit_battle_start(self, room_uid: str, battle_data: Dict[str, Any], priority: int = 2) -> str:
        """提交对战开始任务"""
        task_id = f"start_{room_uid}_{int(time.time() * 1000)}"

        task = AsyncTask(
            task_id=task_id,
            room_uid=room_uid,
            task_type='battle_start',
            payload=battle_data
        )

        if self.processor.submit_task(task, priority):
            logger.debug(f"对战开始任务已提交: {task_id}")
            return task_id
        else:
            logger.error(f"对战开始任务提交失败: {task_id}")
            return None

    def submit_battle_end(self, room_uid: str, battle_data: Dict[str, Any], priority: int = 1) -> str:
        """提交对战结束任务"""
        task_id = f"end_{room_uid}_{int(time.time() * 1000)}"

        task = AsyncTask(
            task_id=task_id,
            room_uid=room_uid,
            task_type='battle_end',
            payload=battle_data
        )

        if self.processor.submit_task(task, priority):
            logger.debug(f"对战结束任务已提交: {task_id}")
            return task_id
        else:
            logger.error(f"对战结束任务提交失败: {task_id}")
            return None

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.processor.get_stats()

    def stop(self):
        """停止处理器"""
        self.processor.stop()


# 全局实例
enhanced_async_processor = EnhancedAsyncProcessor()
battle_async_processor = BattleRoomAsyncProcessor()


def get_enhanced_async_processor() -> EnhancedAsyncProcessor:
    """获取增强异步处理器实例"""
    return enhanced_async_processor


def get_battle_async_processor() -> BattleRoomAsyncProcessor:
    """获取对战房间异步处理器实例"""
    return battle_async_processor
